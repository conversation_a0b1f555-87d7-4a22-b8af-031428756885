{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/constants/index.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Constants\n// Application-wide constants\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nexport const API_CONFIG = {\n  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',\n  TIMEOUT: 10000,\n  RETRY_ATTEMPTS: 3,\n} as const;\n\n// ============================================================================\n// PAGINATION DEFAULTS\n// ============================================================================\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_PAGE_SIZE: 10,\n  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],\n  MAX_PAGE_SIZE: 100,\n} as const;\n\n// ============================================================================\n// VALIDATION RULES\n// ============================================================================\n\nexport const VALIDATION = {\n  USERNAME: {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: 50,\n    PATTERN: /^[a-zA-Z0-9_]+$/,\n  },\n  PASSWORD: {\n    MIN_LENGTH: 6,\n    MAX_LENGTH: 100,\n  },\n  EMAIL: {\n    PATTERN: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  },\n  SEARCH: {\n    MIN_LENGTH: 2,\n    MAX_LENGTH: 100,\n  },\n} as const;\n\n// ============================================================================\n// UI CONSTANTS\n// ============================================================================\n\nexport const UI = {\n  DEBOUNCE_DELAY: 300,\n  ANIMATION_DURATION: 200,\n  NOTIFICATION_DURATION: 4000,\n  MODAL_WIDTH: {\n    SMALL: 400,\n    MEDIUM: 600,\n    LARGE: 800,\n    EXTRA_LARGE: 1000,\n  },\n} as const;\n\n// ============================================================================\n// STATUS COLORS\n// ============================================================================\n\nexport const STATUS_COLORS = {\n  SUCCESS: '#52c41a',\n  ERROR: '#ff4d4f',\n  WARNING: '#faad14',\n  INFO: '#1890ff',\n  PROCESSING: '#722ed1',\n  DEFAULT: '#d9d9d9',\n} as const;\n\n// ============================================================================\n// USER ROLES & TIERS\n// ============================================================================\n\nexport const USER_ROLES = {\n  ADMIN: 'admin',\n  EDITOR: 'editor',\n  MODERATOR: 'moderator',\n} as const;\n\nexport const USER_TIERS = {\n  FREE: 'free',\n  PREMIUM: 'premium',\n  ENTERPRISE: 'enterprise',\n} as const;\n\nexport const ROLE_COLORS = {\n  [USER_ROLES.ADMIN]: 'red',\n  [USER_ROLES.EDITOR]: 'blue',\n  [USER_ROLES.MODERATOR]: 'green',\n} as const;\n\nexport const TIER_COLORS = {\n  [USER_TIERS.FREE]: 'default',\n  [USER_TIERS.PREMIUM]: 'gold',\n  [USER_TIERS.ENTERPRISE]: 'purple',\n} as const;\n\n// ============================================================================\n// API LIMITS\n// ============================================================================\n\nexport const API_LIMITS = {\n  FREE_TIER: 1000,\n  PREMIUM_TIER: 50000,\n  ENTERPRISE_TIER: 200000,\n  UNLIMITED: null,\n} as const;\n\n// ============================================================================\n// DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// ROUTES\n// ============================================================================\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/auth/login',\n  DASHBOARD: '/dashboard',\n  USERS: '/dashboard/users',\n  LEAGUES: '/dashboard/leagues',\n  TEAMS: '/dashboard/teams',\n  FIXTURES: '/dashboard/fixtures',\n  ANALYTICS: '/dashboard/analytics',\n  SYNC: '/dashboard/sync',\n  PROFILE: '/dashboard/profile',\n  SETTINGS: '/dashboard/settings',\n} as const;\n\n// ============================================================================\n// LOCAL STORAGE KEYS\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  ACCESS_TOKEN: 'access_token',\n  REFRESH_TOKEN: 'refresh_token',\n  USER_PREFERENCES: 'user_preferences',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n} as const;\n\n// ============================================================================\n// ERROR MESSAGES\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied. Insufficient permissions.',\n  NOT_FOUND: 'The requested resource was not found.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  SERVER_ERROR: 'Internal server error. Please try again later.',\n  UNKNOWN_ERROR: 'An unknown error occurred.',\n} as const;\n\n// ============================================================================\n// SUCCESS MESSAGES\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  CREATED: 'Created successfully',\n  UPDATED: 'Updated successfully',\n  DELETED: 'Deleted successfully',\n  SAVED: 'Saved successfully',\n  SYNCED: 'Synced successfully',\n  EXPORTED: 'Exported successfully',\n} as const;\n\n// ============================================================================\n// QUERY KEYS\n// ============================================================================\n\nexport const QUERY_KEYS = {\n  AUTH: ['auth'],\n  USERS: ['users'],\n  LEAGUES: ['leagues'],\n  TEAMS: ['teams'],\n  FIXTURES: ['fixtures'],\n  DASHBOARD: ['dashboard'],\n  SYNC: ['sync'],\n} as const;\n\n// ============================================================================\n// PERMISSIONS\n// ============================================================================\n\nexport const PERMISSIONS = {\n  USERS: {\n    VIEW: 'users:view',\n    CREATE: 'users:create',\n    UPDATE: 'users:update',\n    DELETE: 'users:delete',\n  },\n  LEAGUES: {\n    VIEW: 'leagues:view',\n    CREATE: 'leagues:create',\n    UPDATE: 'leagues:update',\n    DELETE: 'leagues:delete',\n  },\n  TEAMS: {\n    VIEW: 'teams:view',\n    CREATE: 'teams:create',\n    UPDATE: 'teams:update',\n    DELETE: 'teams:delete',\n  },\n  FIXTURES: {\n    VIEW: 'fixtures:view',\n    CREATE: 'fixtures:create',\n    UPDATE: 'fixtures:update',\n    DELETE: 'fixtures:delete',\n  },\n  SYNC: {\n    VIEW: 'sync:view',\n    EXECUTE: 'sync:execute',\n  },\n  ANALYTICS: {\n    VIEW: 'analytics:view',\n  },\n} as const;\n\n// ============================================================================\n// EXPORT TYPES\n// ============================================================================\n\nexport type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];\nexport type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS];\nexport type Route = typeof ROUTES[keyof typeof ROUTES];\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS][keyof typeof PERMISSIONS[keyof typeof PERMISSIONS]];\n\n// ============================================================================\n// DEFAULT EXPORT\n// ============================================================================\n\nexport default {\n  API_CONFIG,\n  PAGINATION,\n  VALIDATION,\n  UI,\n  STATUS_COLORS,\n  USER_ROLES,\n  USER_TIERS,\n  ROLE_COLORS,\n  TIER_COLORS,\n  API_LIMITS,\n  DATE_FORMATS,\n  ROUTES,\n  STORAGE_KEYS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  QUERY_KEYS,\n  PERMISSIONS,\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,6BAA6B;AAE7B,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;;;;;;;;;;;;;;;;;;;;;AAExE,MAAM,aAAa;IACxB,UAAU,iEAAmC;IAC7C,SAAS;IACT,gBAAgB;AAClB;AAMO,MAAM,aAAa;IACxB,cAAc;IACd,mBAAmB;IACnB,mBAAmB;QAAC;QAAI;QAAI;QAAI;KAAI;IACpC,eAAe;AACjB;AAMO,MAAM,aAAa;IACxB,UAAU;QACR,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IACA,UAAU;QACR,YAAY;QACZ,YAAY;IACd;IACA,OAAO;QACL,SAAS;IACX;IACA,QAAQ;QACN,YAAY;QACZ,YAAY;IACd;AACF;AAMO,MAAM,KAAK;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,aAAa;QACX,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;AACF;AAMO,MAAM,gBAAgB;IAC3B,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,YAAY;IACZ,SAAS;AACX;AAMO,MAAM,aAAa;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,YAAY;AACd;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,KAAK,CAAC,EAAE;IACpB,CAAC,WAAW,MAAM,CAAC,EAAE;IACrB,CAAC,WAAW,SAAS,CAAC,EAAE;AAC1B;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,IAAI,CAAC,EAAE;IACnB,CAAC,WAAW,OAAO,CAAC,EAAE;IACtB,CAAC,WAAW,UAAU,CAAC,EAAE;AAC3B;AAMO,MAAM,aAAa;IACxB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,WAAW;AACb;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;AAMO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,WAAW;IACX,OAAO;IACP,SAAS;IACT,OAAO;IACP,UAAU;IACV,WAAW;IACX,MAAM;IACN,SAAS;IACT,UAAU;AACZ;AAMO,MAAM,eAAe;IAC1B,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,OAAO;IACP,UAAU;AACZ;AAMO,MAAM,iBAAiB;IAC5B,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,eAAe;AACjB;AAMO,MAAM,mBAAmB;IAC9B,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,UAAU;AACZ;AAMO,MAAM,aAAa;IACxB,MAAM;QAAC;KAAO;IACd,OAAO;QAAC;KAAQ;IAChB,SAAS;QAAC;KAAU;IACpB,OAAO;QAAC;KAAQ;IAChB,UAAU;QAAC;KAAW;IACtB,WAAW;QAAC;KAAY;IACxB,MAAM;QAAC;KAAO;AAChB;AAMO,MAAM,cAAc;IACzB,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,UAAU;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF;uCAee;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/api.ts"], "sourcesContent": ["// APISportsGame CMS - Shared API Utilities\n// Common API utilities và error handling\n\nimport { AxiosError } from 'axios';\n\n// ============================================================================\n// API ERROR HANDLING\n// ============================================================================\n\nexport interface ApiErrorResponse {\n  statusCode: number;\n  message: string | string[];\n  error: string;\n  timestamp: string;\n  path: string;\n}\n\nexport class ApiError extends Error {\n  public statusCode: number;\n  public response?: ApiErrorResponse;\n\n  constructor(error: AxiosError) {\n    const response = error.response?.data as ApiErrorResponse;\n    const message = Array.isArray(response?.message) \n      ? response.message.join(', ')\n      : response?.message || error.message || 'An error occurred';\n\n    super(message);\n    this.name = 'ApiError';\n    this.statusCode = error.response?.status || 500;\n    this.response = response;\n  }\n}\n\n// ============================================================================\n// API RESPONSE UTILITIES\n// ============================================================================\n\nexport const apiUtils = {\n  /**\n   * Handle API errors consistently\n   */\n  handleError: (error: unknown): ApiError => {\n    if (error instanceof AxiosError) {\n      return new ApiError(error);\n    }\n    \n    if (error instanceof Error) {\n      const apiError = new ApiError({} as AxiosError);\n      apiError.message = error.message;\n      return apiError;\n    }\n\n    const apiError = new ApiError({} as AxiosError);\n    apiError.message = 'Unknown error occurred';\n    return apiError;\n  },\n\n  /**\n   * Extract error message for display\n   */\n  getErrorMessage: (error: unknown): string => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.message;\n  },\n\n  /**\n   * Check if error is specific status code\n   */\n  isErrorStatus: (error: unknown, statusCode: number): boolean => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.statusCode === statusCode;\n  },\n\n  /**\n   * Check if error is unauthorized\n   */\n  isUnauthorized: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 401);\n  },\n\n  /**\n   * Check if error is forbidden\n   */\n  isForbidden: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 403);\n  },\n\n  /**\n   * Check if error is not found\n   */\n  isNotFound: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 404);\n  },\n\n  /**\n   * Check if error is validation error\n   */\n  isValidationError: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 400);\n  },\n\n  /**\n   * Format pagination params\n   */\n  formatPaginationParams: (page: number, limit: number) => ({\n    page,\n    limit,\n    offset: (page - 1) * limit,\n  }),\n\n  /**\n   * Format filter params (remove undefined values)\n   */\n  formatFilterParams: (filters: Record<string, any>) => {\n    const cleanFilters: Record<string, any> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        cleanFilters[key] = value;\n      }\n    });\n\n    return cleanFilters;\n  },\n\n  /**\n   * Build query string from params\n   */\n  buildQueryString: (params: Record<string, any>): string => {\n    const cleanParams = apiUtils.formatFilterParams(params);\n    const searchParams = new URLSearchParams();\n\n    Object.entries(cleanParams).forEach(([key, value]) => {\n      if (Array.isArray(value)) {\n        value.forEach(v => searchParams.append(key, String(v)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    });\n\n    return searchParams.toString();\n  },\n};\n\n// ============================================================================\n// PAGINATION UTILITIES\n// ============================================================================\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface PaginationMeta {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: PaginationMeta;\n}\n\nexport const paginationUtils = {\n  /**\n   * Calculate total pages\n   */\n  calculateTotalPages: (total: number, limit: number): number => {\n    return Math.ceil(total / limit);\n  },\n\n  /**\n   * Calculate offset from page and limit\n   */\n  calculateOffset: (page: number, limit: number): number => {\n    return (page - 1) * limit;\n  },\n\n  /**\n   * Get pagination info for display\n   */\n  getPaginationInfo: (meta: PaginationMeta) => {\n    const start = paginationUtils.calculateOffset(meta.page, meta.limit) + 1;\n    const end = Math.min(start + meta.limit - 1, meta.total);\n    \n    return {\n      start,\n      end,\n      total: meta.total,\n      hasNext: meta.page < meta.totalPages,\n      hasPrev: meta.page > 1,\n    };\n  },\n};\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport default apiUtils;\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,yCAAyC;;;;;;;AAEzC;;AAcO,MAAM,iBAAiB;IACrB,WAAmB;IACnB,SAA4B;IAEnC,YAAY,KAAiB,CAAE;QAC7B,MAAM,WAAW,MAAM,QAAQ,EAAE;QACjC,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,WACpC,SAAS,OAAO,CAAC,IAAI,CAAC,QACtB,UAAU,WAAW,MAAM,OAAO,IAAI;QAE1C,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG,MAAM,QAAQ,EAAE,UAAU;QAC5C,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAMO,MAAM,WAAW;IACtB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,iBAAiB,8IAAA,CAAA,aAAU,EAAE;YAC/B,OAAO,IAAI,SAAS;QACtB;QAEA,IAAI,iBAAiB,OAAO;YAC1B,MAAM,WAAW,IAAI,SAAS,CAAC;YAC/B,SAAS,OAAO,GAAG,MAAM,OAAO;YAChC,OAAO;QACT;QAEA,MAAM,WAAW,IAAI,SAAS,CAAC;QAC/B,SAAS,OAAO,GAAG;QACnB,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,OAAO;IACzB;IAEA;;GAEC,GACD,eAAe,CAAC,OAAgB;QAC9B,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,UAAU,KAAK;IACjC;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,wBAAwB,CAAC,MAAc,QAAkB,CAAC;YACxD;YACA;YACA,QAAQ,CAAC,OAAO,CAAC,IAAI;QACvB,CAAC;IAED;;GAEC,GACD,oBAAoB,CAAC;QACnB,MAAM,eAAoC,CAAC;QAE3C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,YAAY,CAAC,IAAI,GAAG;YACtB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,cAAc,SAAS,kBAAkB,CAAC;QAChD,MAAM,eAAe,IAAI;QAEzB,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,aAAa,MAAM,CAAC,KAAK,OAAO;YACrD,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;QAEA,OAAO,aAAa,QAAQ;IAC9B;AACF;AAuBO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,qBAAqB,CAAC,OAAe;QACnC,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC3B;IAEA;;GAEC,GACD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,CAAC,OAAO,CAAC,IAAI;IACtB;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,MAAM,QAAQ,gBAAgB,eAAe,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI;QACvE,MAAM,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,KAAK,GAAG,GAAG,KAAK,KAAK;QAEvD,OAAO;YACL;YACA;YACA,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,IAAI,GAAG,KAAK,UAAU;YACpC,SAAS,KAAK,IAAI,GAAG;QACvB;IACF;AACF;uCAMe"}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/api/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth API Module\n// Authentication API calls\n\nimport axios, { AxiosInstance } from 'axios';\nimport { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';\nimport { apiUtils } from '@/shared/utils/api';\nimport {\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  RefreshTokenRequest,\n  RefreshTokenResponse,\n  SystemUser,\n  RegisteredUser,\n} from '../types';\n\n// ============================================================================\n// AUTH API ENDPOINTS\n// ============================================================================\n\nconst AUTH_ENDPOINTS = {\n  // SystemUser authentication endpoints\n  login: '/system-auth/login',\n  profile: '/system-auth/profile',\n  refresh: '/system-auth/refresh',\n  logout: '/system-auth/logout',\n  logoutAll: '/system-auth/logout-all',\n  createUser: '/system-auth/create-user',\n  changePassword: '/system-auth/change-password',\n\n  // RegisteredUser endpoints (for future User Manager module)\n  userRegister: '/users/register',\n  userLogin: '/users/login',\n  verifyEmail: '/users/verify-email',\n} as const;\n\n// ============================================================================\n// AUTH API CLIENT\n// ============================================================================\n\nclass AuthApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_CONFIG.BASE_URL,\n      timeout: API_CONFIG.TIMEOUT,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - add JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(apiUtils.handleError(error))\n    );\n\n    // Response interceptor - handle errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.clearTokens();\n            // Redirect to login will be handled by auth store\n            throw apiUtils.handleError(refreshError);\n          }\n        }\n\n        throw apiUtils.handleError(error);\n      }\n    );\n  }\n\n  // ========================================================================\n  // TOKEN MANAGEMENT\n  // ========================================================================\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);\n    }\n  }\n\n  private getRefreshToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n    return null;\n  }\n\n  private setRefreshToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);\n    }\n  }\n\n  private clearTokens(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n  }\n\n  // ========================================================================\n  // AUTH API METHODS\n  // ========================================================================\n\n  /**\n   * Login user (both SystemUser and RegisteredUser)\n   */\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await this.instance.post<LoginResponse>(\n        AUTH_ENDPOINTS.login,\n        credentials\n      );\n\n      const { accessToken, refreshToken } = response.data;\n      this.setToken(accessToken);\n      this.setRefreshToken(refreshToken);\n\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Get current user profile\n   */\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    try {\n      const response = await this.instance.get<SystemUser | RegisteredUser>(\n        AUTH_ENDPOINTS.profile\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Refresh access token\n   */\n  async refreshToken(): Promise<RefreshTokenResponse> {\n    try {\n      const refreshToken = this.getRefreshToken();\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await this.instance.post<RefreshTokenResponse>(\n        AUTH_ENDPOINTS.refresh,\n        { refresh_token: refreshToken }\n      );\n\n      const { accessToken, refreshToken: newRefreshToken } = response.data;\n      this.setToken(accessToken);\n      this.setRefreshToken(newRefreshToken);\n\n      return response.data;\n    } catch (error) {\n      this.clearTokens();\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logout);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Create new system user (admin only) - sử dụng system-auth endpoint\n   */\n  async createSystemUser(userData: RegisterRequest & { role: string }): Promise<SystemUser> {\n    try {\n      const response = await this.instance.post<SystemUser>(\n        AUTH_ENDPOINTS.createUser,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Change password for current user\n   */\n  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.changePassword, data);\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout from all devices\n   */\n  async logoutAllDevices(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logoutAll);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout all devices API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Register new registered user\n   */\n  async registerUser(userData: RegisterRequest): Promise<RegisteredUser> {\n    try {\n      const response = await this.instance.post<RegisteredUser>(\n        AUTH_ENDPOINTS.userRegister,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Verify email for registered user\n   */\n  async verifyEmail(token: string): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.verifyEmail, { token });\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  /**\n   * Get current access token\n   */\n  getCurrentToken(): string | null {\n    return this.getToken();\n  }\n}\n\n// ============================================================================\n// EXPORT SINGLETON INSTANCE\n// ============================================================================\n\nexport const authApi = new AuthApiClient();\nexport default authApi;\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,2BAA2B;;;;;AAG3B;AACA;AAFA;;;;AAaA,+EAA+E;AAC/E,qBAAqB;AACrB,+EAA+E;AAE/E,MAAM,iBAAiB;IACrB,sCAAsC;IACtC,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,WAAW;IACX,YAAY;IACZ,gBAAgB;IAEhB,4DAA4D;IAC5D,cAAc;IACd,WAAW;IACX,aAAa;AACf;AAEA,+EAA+E;AAC/E,kBAAkB;AAClB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS,mIAAA,CAAA,aAAU,CAAC,QAAQ;YAC5B,SAAS,mIAAA,CAAA,aAAU,CAAC,OAAO;YAC3B,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAGjD,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,WAAW;oBAChB,kDAAkD;oBAClD,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;gBAC7B;YACF;YAEA,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IAEJ;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAEnE,WAA0B;QAChC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,SAAS,KAAa,EAAQ;QACpC,uCAAmC;;QAEnC;IACF;IAEQ,kBAAiC;QACvC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,gBAAgB,KAAa,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEQ,cAAoB;QAC1B,uCAAmC;;QAGnC;IACF;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E;;GAEC,GACD,MAAM,MAAM,WAAyB,EAA0B;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,KAAK,EACpB;YAGF,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,SAAS,IAAI;YACnD,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,aAAmD;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,eAAe,OAAO;YAExB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAA8C;QAClD,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,OAAO,EACtB;gBAAE,eAAe;YAAa;YAGhC,MAAM,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE,GAAG,SAAS,IAAI;YACpE,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,SAAwB;QAC5B,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,MAAM;QAChD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,2BAA2B;QAC1C,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAA4C,EAAuB;QACxF,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,UAAU,EACzB;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,IAAsD,EAAiB;QAC1F,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,cAAc,EAAE;QAC1D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,uCAAuC;QACtD,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAyB,EAA2B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,YAAY,EAC3B;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAiB;QAC9C,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,WAAW,EAAE;gBAAE;YAAM;QAC/D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,kBAAiC;QAC/B,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF;AAMO,MAAM,UAAU,IAAI;uCACZ"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/types/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth Module Types\n// Authentication related types\n\nimport { BaseEntity } from '@/shared/types/common';\n\n// ============================================================================\n// USER TYPES\n// ============================================================================\n\nexport interface SystemUser extends BaseEntity {\n  username: string;\n  email: string;\n  role: 'admin' | 'editor' | 'moderator';\n  isActive: boolean;\n  lastLoginAt: Date;\n}\n\nexport interface RegisteredUser extends BaseEntity {\n  username: string;\n  email: string;\n  tier: 'free' | 'premium' | 'enterprise';\n  isActive: boolean;\n  isEmailVerified: boolean;\n  apiCallsUsed: number;\n  apiCallsLimit: number | null;\n  subscriptionEndDate: Date | null;\n  lastLoginAt: Date;\n}\n\nexport type User = SystemUser | RegisteredUser;\n\n// ============================================================================\n// ROLE & TIER TYPES\n// ============================================================================\n\nexport type SystemRole = 'admin' | 'editor' | 'moderator';\nexport type RegisteredUserTier = 'free' | 'premium' | 'enterprise';\n\n// ============================================================================\n// JWT TOKEN TYPES\n// ============================================================================\n\nexport interface SystemUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  role: SystemRole;\n  userType: 'system';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface RegisteredUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  tier: RegisteredUserTier;\n  userType: 'registered';\n  isEmailVerified: boolean;\n  iat?: number;\n  exp?: number;\n}\n\nexport type UserToken = SystemUserToken | RegisteredUserToken;\n\n// ============================================================================\n// AUTH REQUEST/RESPONSE TYPES\n// ============================================================================\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  accessToken: string;\n  refreshToken: string;\n  user?: SystemUser | RegisteredUser; // User có thể không có trong response\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport interface RefreshTokenRequest {\n  refresh_token: string;\n}\n\nexport interface RefreshTokenResponse {\n  accessToken: string;\n  refreshToken: string;\n}\n\n// ============================================================================\n// AUTH STATE TYPES\n// ============================================================================\n\nexport interface AuthState {\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nexport interface AuthActions {\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  refreshToken: () => Promise<void>;\n  clearError: () => void;\n}\n\n// ============================================================================\n// PERMISSION TYPES\n// ============================================================================\n\nexport interface PermissionState {\n  // User type checks\n  isSystemUser: boolean;\n  isRegisteredUser: boolean;\n\n  // Role checks (SystemUser)\n  isAdmin: boolean;\n  isEditor: boolean;\n  isModerator: boolean;\n\n  // Tier checks (RegisteredUser)\n  isFree: boolean;\n  isPremium: boolean;\n  isEnterprise: boolean;\n\n  // Permission checks\n  canManageUsers: boolean;\n  canManageLeagues: boolean;\n  canManageTeams: boolean;\n  canManageFixtures: boolean;\n  canSync: boolean;\n  canViewAnalytics: boolean;\n\n  // Current user\n  currentUser: SystemUser | RegisteredUser | null;\n}\n\n// ============================================================================\n// AUTH HOOK TYPES\n// ============================================================================\n\nexport interface UseAuthReturn extends AuthState, AuthActions { }\n\nexport interface UsePermissionsReturn extends PermissionState { }\n\n// ============================================================================\n// TYPE GUARDS\n// ============================================================================\n\nexport const isSystemUser = (user: SystemUser | RegisteredUser | null): user is SystemUser => {\n  return user !== null && 'role' in user;\n};\n\nexport const isRegisteredUser = (user: SystemUser | RegisteredUser | null): user is RegisteredUser => {\n  return user !== null && 'tier' in user;\n};\n\nexport const isSystemUserToken = (token: UserToken): token is SystemUserToken => {\n  return token.userType === 'system';\n};\n\nexport const isRegisteredUserToken = (token: UserToken): token is RegisteredUserToken => {\n  return token.userType === 'registered';\n};\n\n// ============================================================================\n// EXPORT ALL\n// ============================================================================\n\n// Remove circular export\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,+BAA+B;;;;;;;AA4JxB,MAAM,eAAe,CAAC;IAC3B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,QAAQ,KAAK;AAC5B;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,MAAM,QAAQ,KAAK;AAC5B,GAEA,+EAA+E;CAC/E,aAAa;CACb,+EAA+E;CAE/E,yBAAyB"}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho SystemUser authentication (FECMS chỉ sử dụng SystemUser)\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi } from '@/modules/auth/api';\nimport {\n  SystemUser,\n  RegisteredUser,\n  LoginRequest,\n  AuthState,\n  AuthActions,\n  UseAuthReturn,\n  UsePermissionsReturn,\n  isSystemUser,\n  isRegisteredUser,\n} from '@/modules/auth/types';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthStoreState extends AuthState, AuthActions {\n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthStoreState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await authApi.login(credentials);\n\n          // Nếu response có user, sử dụng nó. Nếu không, gọi getProfile\n          let user = response.user;\n          if (!user) {\n            user = await authApi.getProfile();\n          }\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Đăng nhập thất bại';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTIONS\n      // ========================================================================\n      logout: () => {\n        try {\n          authApi.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      logoutAllDevices: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.logoutAllDevices();\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to logout from all devices';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // SYSTEM USER MANAGEMENT ACTIONS\n      // ========================================================================\n      createSystemUser: async (userData: any) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const newUser = await authApi.createSystemUser(userData);\n          set({ isLoading: false, error: null });\n          return newUser;\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to create system user';\n\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      changePassword: async (data: { currentPassword: string; newPassword: string }) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.changePassword(data);\n          set({ isLoading: false, error: null });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to change password';\n\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const user = await authApi.getProfile();\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Không thể lấy thông tin người dùng';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return isSystemUser(user);\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return isRegisteredUser(user);\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n\n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n\n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n\n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n\n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n\n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n\n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    logoutAllDevices,\n    getProfile,\n    createSystemUser,\n    changePassword,\n    clearError\n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    logoutAllDevices,\n    getProfile,\n    createSystemUser,\n    changePassword,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,6EAA6E;;;;;;AAI7E;AACA;AAHA;AACA;;;;;AA8BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,sIAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAErC,8DAA8D;gBAC9D,IAAI,OAAO,SAAS,IAAI;gBACxB,IAAI,CAAC,MAAM;oBACT,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBACjC;gBAEA,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,sIAAA,CAAA,UAAO,CAAC,MAAM;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,kBAAkB;YAChB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,sIAAA,CAAA,UAAO,CAAC,gBAAgB;gBAC9B,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,iCAAiC;QACjC,2EAA2E;QAC3E,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,UAAU,MAAM,sIAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;gBAC/C,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAK;gBACpC,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,sIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAC7B,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAK;YACtC,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBAErC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;QACtB;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,cAAc,EACd,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layouts/dashboard-layout.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Layout\n// Main layout cho dashboard với navigation và authentication\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Typography,\n  Space,\n  Button,\n  Badge,\n  Spin,\n  Alert\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SyncOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\nimport { useAuth, usePermissions } from '@/stores/auth-store';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD LAYOUT COMPONENT\n// ============================================================================\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, isLoading, logout, getProfile } = useAuth();\n  const permissions = usePermissions();\n\n  const [collapsed, setCollapsed] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Check authentication và load profile\n  useEffect(() => {\n    if (!isAuthenticated && !isLoading) {\n      router.push('/auth/login');\n      return;\n    }\n\n    if (isAuthenticated && !user) {\n      getProfile().catch(() => {\n        router.push('/auth/login');\n      });\n    }\n  }, [isAuthenticated, isLoading, user, router, getProfile]);\n\n  // ========================================================================\n  // MENU CONFIGURATION\n  // ========================================================================\n\n  const getMenuItems = () => {\n    const items = [\n      {\n        key: '/dashboard',\n        icon: <DashboardOutlined />,\n        label: 'Dashboard',\n      }\n    ];\n\n    // User System Management - chỉ admin\n    if (permissions.canManageUsers) {\n      items.push({\n        key: '/dashboard/system-users',\n        icon: <UserOutlined />,\n        label: 'User System',\n      });\n\n      // User Manager (RegisteredUser) - chỉ admin\n      items.push({\n        key: '/dashboard/user-manager',\n        icon: <TeamOutlined />,\n        label: 'User Manager',\n      });\n    }\n\n    // Sports Data Management\n    if (permissions.canManageLeagues) {\n      items.push({\n        key: '/dashboard/leagues',\n        icon: <TrophyOutlined />,\n        label: 'Quản lý Leagues',\n      });\n    }\n\n    if (permissions.canManageTeams) {\n      items.push({\n        key: '/dashboard/teams',\n        icon: <TeamOutlined />,\n        label: 'Quản lý Teams',\n      });\n    }\n\n    if (permissions.canManageFixtures) {\n      items.push({\n        key: '/dashboard/fixtures',\n        icon: <CalendarOutlined />,\n        label: 'Quản lý Fixtures',\n      });\n    }\n\n    // Analytics\n    if (permissions.canViewAnalytics) {\n      items.push({\n        key: '/dashboard/analytics',\n        icon: <BarChartOutlined />,\n        label: 'Analytics',\n      });\n    }\n\n    // Sync Operations\n    if (permissions.canSync) {\n      items.push({\n        key: '/dashboard/sync',\n        icon: <SyncOutlined />,\n        label: 'Sync Operations',\n      });\n    }\n\n    return items;\n  };\n\n  // ========================================================================\n  // USER DROPDOWN MENU\n  // ========================================================================\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Thông tin cá nhân',\n      onClick: () => router.push('/dashboard/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Cài đặt',\n      onClick: () => router.push('/dashboard/settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Đăng xuất',\n      onClick: () => {\n        logout();\n        router.push('/auth/login');\n      },\n    },\n  ];\n\n  // ========================================================================\n  // HANDLERS\n  // ========================================================================\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    router.push(key);\n  };\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // ========================================================================\n  // LOADING STATE\n  // ========================================================================\n\n  if (!mounted || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Spin size=\"large\" tip=\"Đang tải...\" />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // UNAUTHENTICATED STATE\n  // ========================================================================\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Alert\n          message=\"Chưa đăng nhập\"\n          description=\"Vui lòng đăng nhập để tiếp tục\"\n          type=\"warning\"\n          showIcon\n        />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* Sidebar */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        theme=\"dark\"\n        width={256}\n        className=\"shadow-lg\"\n      >\n        {/* Logo */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-700\">\n          <Space>\n            <ApiOutlined className=\"text-white text-xl\" />\n            {!collapsed && (\n              <Title level={4} className=\"text-white m-0\">\n                CMS\n              </Title>\n            )}\n          </Space>\n        </div>\n\n        {/* Navigation Menu */}\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          items={getMenuItems()}\n          onClick={handleMenuClick}\n          className=\"border-r-0\"\n        />\n      </Sider>\n\n      {/* Main Layout */}\n      <Layout>\n        {/* Header */}\n        <Header className=\"bg-white shadow-sm px-4 flex items-center justify-between\">\n          {/* Left side */}\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={toggleCollapsed}\n              className=\"text-lg\"\n            />\n            <Title level={4} className=\"m-0\">\n              APISportsGame CMS\n            </Title>\n          </Space>\n\n          {/* Right side */}\n          <Space size=\"middle\">\n            {/* Notifications */}\n            <Badge count={0} showZero={false}>\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            {/* User Info */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar\n                  size=\"small\"\n                  icon={<UserOutlined />}\n                  className=\"bg-blue-500\"\n                />\n                <div className=\"text-left\">\n                  <div className=\"text-sm font-medium\">\n                    {user?.username}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {permissions.isSystemUser && `${(user as any)?.role}`}\n                    {permissions.isRegisteredUser && `${(user as any)?.tier}`}\n                  </div>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        {/* Content */}\n        <Content className=\"p-6 bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA,uCAAuC;AACvC,6DAA6D;AAE7D;AACA;AA4BA;AA3BA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAoCA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAU3B,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvE,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,WAAW;YAClC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,mBAAmB,CAAC,MAAM;YAC5B,aAAa,KAAK,CAAC;gBACjB,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;QAAM;QAAQ;KAAW;IAEzD,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,eAAe;QACnB,MAAM,QAAQ;YACZ;gBACE,KAAK;gBACL,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;gBACxB,OAAO;YACT;SACD;QAED,qCAAqC;QACrC,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;YAEA,4CAA4C;YAC5C,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;gBACrB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,iBAAiB,EAAE;YACjC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,YAAY;QACZ,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;KACD;IAED,2EAA2E;IAC3E,WAAW;IACX,2EAA2E;IAE3E,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,IAAI,CAAC,WAAW,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;gBAAC,MAAK;gBAAQ,KAAI;;;;;;;;;;;IAG7B;IAEA,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;;;;;;;;;;;IAIhB;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,8OAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAM;gBACN,OAAO;gBACP,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,gNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,CAAC,2BACA,8OAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAiB;;;;;;;;;;;;;;;;;kCAQlD,8OAAC,8KAAA,CAAA,OAAI;wBACH,OAAM;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAKd,8OAAC,kLAAA,CAAA,SAAM;;kCAEL,8OAAC;wBAAO,WAAU;;0CAEhB,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,8OAAC,8NAAA,CAAA,qBAAkB;;;;mEAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAM;;;;;;;;;;;;0CAMnC,8OAAC,gMAAA,CAAA,QAAK;gCAAC,MAAK;;kDAEV,8OAAC,gLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,UAAU;kDACzB,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAKd,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,WAAU;wCACV,KAAK;kDAEL,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kLAAA,CAAA,SAAM;oDACL,MAAK;oDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oDACnB,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,MAAM;;;;;;sEAET,8OAAC;4DAAI,WAAU;;gEACZ,YAAY,YAAY,IAAI,GAAI,MAAc,MAAM;gEACpD,YAAY,gBAAgB,IAAI,GAAI,MAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrE,8OAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/leagues/types/index.ts"], "sourcesContent": ["// APISportsGame CMS - Leagues Module Types\n// League management related types\n\nimport { BaseEntity, BaseFilters, PaginatedResponse } from '@/shared/types/common';\n\n// ============================================================================\n// LEAGUE ENTITY TYPES\n// ============================================================================\n\n// League interface từ CMS_DEVELOPMENT_GUIDE.md\nexport interface League extends BaseEntity {\n  apiFootballId: number;\n  name: string;\n  country: string;\n  logo: string;\n  flag: string;\n  season: number;\n  isActive: boolean;\n}\n\n// ============================================================================\n// FORM TYPES\n// ============================================================================\n\nexport interface CreateLeagueForm {\n  apiFootballId: number;\n  name: string;\n  country: string;\n  logo?: string;\n  flag?: string;\n  season: number;\n  isActive?: boolean;\n}\n\nexport interface UpdateLeagueForm {\n  apiFootballId?: number;\n  name?: string;\n  country?: string;\n  logo?: string;\n  flag?: string;\n  season?: number;\n  isActive?: boolean;\n}\n\n// ============================================================================\n// FILTER TYPES\n// ============================================================================\n\nexport interface LeagueFilters extends BaseFilters {\n  country?: string;\n  season?: number;\n  current?: boolean;\n  hasFixtures?: boolean;\n  hasStandings?: boolean;\n  createdFrom?: string;\n  createdTo?: string;\n}\n\n// ============================================================================\n// API RESPONSE TYPES\n// ============================================================================\n\nexport interface LeaguesResponse extends PaginatedResponse<League> { }\n\nexport interface LeagueStatsResponse {\n  total: number;\n  active: number;\n  current: number;\n  byCountry: Record<string, number>;\n  bySeason: Record<string, number>;\n}\n\n// ============================================================================\n// COMPONENT PROPS TYPES\n// ============================================================================\n\nexport interface LeagueTableProps {\n  leagues: League[];\n  loading?: boolean;\n  onEdit?: (league: League) => void;\n  onDelete?: (league: League) => void;\n  onAdd?: () => void;\n  onRefresh?: () => void;\n  onExport?: () => void;\n  onSync?: () => void;\n  pagination?: {\n    current: number;\n    pageSize: number;\n    total: number;\n    onChange: (page: number, pageSize: number) => void;\n  };\n  filters?: LeagueFilters;\n  onFiltersChange?: (filters: LeagueFilters) => void;\n}\n\nexport interface LeagueFormProps {\n  mode: 'create' | 'edit';\n  initialValues?: Partial<League>;\n  loading?: boolean;\n  onSubmit: (values: CreateLeagueForm | UpdateLeagueForm) => void;\n  onCancel: () => void;\n}\n\nexport interface LeagueCardProps {\n  league: League;\n  onEdit?: (league: League) => void;\n  onDelete?: (league: League) => void;\n  onView?: (league: League) => void;\n}\n\n// ============================================================================\n// HOOK TYPES\n// ============================================================================\n\nexport interface UseLeaguesOptions {\n  filters?: LeagueFilters;\n  pagination?: {\n    page: number;\n    limit: number;\n  };\n  enabled?: boolean;\n}\n\nexport interface UseLeaguesReturn {\n  leagues: League[];\n  loading: boolean;\n  error: string | null;\n  pagination: {\n    current: number;\n    pageSize: number;\n    total: number;\n    totalPages: number;\n  };\n  refetch: () => void;\n}\n\nexport interface UseLeagueMutationsReturn {\n  createLeague: {\n    mutate: (data: CreateLeagueForm) => Promise<void>;\n    loading: boolean;\n    error: string | null;\n  };\n  updateLeague: {\n    mutate: (id: number, data: UpdateLeagueForm) => Promise<void>;\n    loading: boolean;\n    error: string | null;\n  };\n  deleteLeague: {\n    mutate: (id: number) => Promise<void>;\n    loading: boolean;\n    error: string | null;\n  };\n  syncLeagues: {\n    mutate: () => Promise<void>;\n    loading: boolean;\n    error: string | null;\n  };\n}\n\n// ============================================================================\n// SYNC TYPES\n// ============================================================================\n\nexport interface LeagueSyncStatus {\n  isRunning: boolean;\n  lastSync: Date | null;\n  nextScheduled: Date | null;\n  totalSynced: number;\n  errors: string[];\n  progress?: {\n    current: number;\n    total: number;\n    percentage: number;\n  };\n}\n\nexport interface LeagueSyncOptions {\n  countries?: string[];\n  seasons?: number[];\n  forceUpdate?: boolean;\n  includeInactive?: boolean;\n}\n\n// ============================================================================\n// COVERAGE TYPES\n// ============================================================================\n\nexport interface CoverageConfig {\n  fixtures: {\n    events: boolean;\n    lineups: boolean;\n    statistics_fixtures: boolean;\n    statistics_players: boolean;\n  };\n  standings: boolean;\n  players: boolean;\n  top_scorers: boolean;\n  top_assists: boolean;\n  top_cards: boolean;\n  injuries: boolean;\n  predictions: boolean;\n  odds: boolean;\n}\n\n// ============================================================================\n// UTILITY TYPES\n// ============================================================================\n\nexport type LeagueStatus = 'active' | 'inactive' | 'upcoming' | 'finished';\nexport type LeagueSeason = number;\nexport type LeagueCountry = string;\n\n// ============================================================================\n// TYPE GUARDS\n// ============================================================================\n\nexport const isActiveLeague = (league: League): boolean => {\n  return league.current && new Date(league.end) > new Date();\n};\n\nexport const isUpcomingLeague = (league: League): boolean => {\n  return new Date(league.start) > new Date();\n};\n\nexport const isFinishedLeague = (league: League): boolean => {\n  return new Date(league.end) < new Date();\n};\n\nexport const getLeagueStatus = (league: League): LeagueStatus => {\n  if (isUpcomingLeague(league)) return 'upcoming';\n  if (isFinishedLeague(league)) return 'finished';\n  if (isActiveLeague(league)) return 'active';\n  return 'inactive';\n};\n\n// ============================================================================\n// CONSTANTS\n// ============================================================================\n\nexport const LEAGUE_COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France',\n  'Netherlands', 'Portugal', 'Brazil', 'Argentina',\n  'Mexico', 'United States', 'Japan', 'South Korea'\n] as const;\n\nexport const CURRENT_SEASON = new Date().getFullYear();\n\nexport const LEAGUE_SEASONS = Array.from(\n  { length: 10 },\n  (_, i) => CURRENT_SEASON - i\n);\n\n// ============================================================================\n// EXPORT ALL\n// ============================================================================\n\n// Remove circular export\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,kCAAkC;;;;;;;;;;AAuN3B,MAAM,iBAAiB,CAAC;IAC7B,OAAO,OAAO,OAAO,IAAI,IAAI,KAAK,OAAO,GAAG,IAAI,IAAI;AACtD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI;AACtC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,IAAI,KAAK,OAAO,GAAG,IAAI,IAAI;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,iBAAiB,SAAS,OAAO;IACrC,IAAI,iBAAiB,SAAS,OAAO;IACrC,IAAI,eAAe,SAAS,OAAO;IACnC,OAAO;AACT;AAMO,MAAM,mBAAmB;IAC9B;IAAW;IAAS;IAAW;IAAS;IACxC;IAAe;IAAY;IAAU;IACrC;IAAU;IAAiB;IAAS;CACrC;AAEM,MAAM,iBAAiB,IAAI,OAAO,WAAW;AAE7C,MAAM,iBAAiB,MAAM,IAAI,CACtC;IAAE,QAAQ;AAAG,GACb,CAAC,GAAG,IAAM,iBAAiB,IAG7B,+EAA+E;CAC/E,aAAa;CACb,+EAA+E;CAE/E,yBAAyB"}}, {"offset": {"line": 1554, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/date.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Date Utilities\n// Centralized date handling với dayjs\n\nimport dayjs from 'dayjs';\nimport relativeTime from 'dayjs/plugin/relativeTime';\nimport utc from 'dayjs/plugin/utc';\nimport timezone from 'dayjs/plugin/timezone';\nimport customParseFormat from 'dayjs/plugin/customParseFormat';\n\n// ============================================================================\n// DAYJS CONFIGURATION\n// ============================================================================\n\n// Extend dayjs với required plugins\ndayjs.extend(relativeTime);\ndayjs.extend(utc);\ndayjs.extend(timezone);\ndayjs.extend(customParseFormat);\n\n// ============================================================================\n// DATE UTILITIES\n// ============================================================================\n\nexport const dateUtils = {\n  /**\n   * Format date to relative time (e.g., \"2 hours ago\")\n   */\n  fromNow: (date: string | Date) => {\n    return dayjs(date).fromNow();\n  },\n\n  /**\n   * Format date to standard format\n   */\n  format: (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {\n    return dayjs(date).format(format);\n  },\n\n  /**\n   * Format date for display\n   */\n  formatDisplay: (date: string | Date) => {\n    return dayjs(date).format('MMM DD, YYYY');\n  },\n\n  /**\n   * Format date with time for display\n   */\n  formatDateTime: (date: string | Date) => {\n    return dayjs(date).format('MMM DD, YYYY HH:mm');\n  },\n\n  /**\n   * Check if date is today\n   */\n  isToday: (date: string | Date) => {\n    return dayjs(date).isSame(dayjs(), 'day');\n  },\n\n  /**\n   * Check if date is yesterday\n   */\n  isYesterday: (date: string | Date) => {\n    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day');\n  },\n\n  /**\n   * Get start of day\n   */\n  startOfDay: (date?: string | Date) => {\n    return dayjs(date).startOf('day');\n  },\n\n  /**\n   * Get end of day\n   */\n  endOfDay: (date?: string | Date) => {\n    return dayjs(date).endOf('day');\n  },\n\n  /**\n   * Convert to ISO string\n   */\n  toISOString: (date: string | Date) => {\n    return dayjs(date).toISOString();\n  },\n\n  /**\n   * Parse date for form inputs\n   */\n  parseForForm: (date: string | Date | null) => {\n    if (!date) return null;\n    return dayjs(date);\n  },\n\n  /**\n   * Get current timestamp\n   */\n  now: () => {\n    return dayjs();\n  },\n\n  /**\n   * Add time to date\n   */\n  add: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {\n    return dayjs(date).add(amount, unit);\n  },\n\n  /**\n   * Subtract time from date\n   */\n  subtract: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {\n    return dayjs(date).subtract(amount, unit);\n  },\n\n  /**\n   * Check if date is before another date\n   */\n  isBefore: (date1: string | Date, date2: string | Date) => {\n    return dayjs(date1).isBefore(dayjs(date2));\n  },\n\n  /**\n   * Check if date is after another date\n   */\n  isAfter: (date1: string | Date, date2: string | Date) => {\n    return dayjs(date1).isAfter(dayjs(date2));\n  },\n\n  /**\n   * Get difference between dates\n   */\n  diff: (date1: string | Date, date2: string | Date, unit?: dayjs.QUnitType) => {\n    return dayjs(date1).diff(dayjs(date2), unit);\n  },\n};\n\n// ============================================================================\n// COMMON DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport { dayjs };\nexport default dateUtils;\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,sCAAsC;;;;;;AAEtC;AACA;AACA;AACA;AACA;;;;;;AAEA,+EAA+E;AAC/E,sBAAsB;AACtB,+EAA+E;AAE/E,oCAAoC;AACpC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,+IAAA,CAAA,UAAY;AACzB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,sIAAA,CAAA,UAAG;AAChB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,2IAAA,CAAA,UAAQ;AACrB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,oJAAA,CAAA,UAAiB;AAMvB,MAAM,YAAY;IACvB;;GAEC,GACD,SAAS,CAAC;QACR,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO;IAC5B;IAEA;;GAEC,GACD,QAAQ,CAAC,MAAqB,SAAS,qBAAqB;QAC1D,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,SAAS,CAAC;QACR,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,KAAK;IACrC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,IAAI,QAAQ,CAAC,GAAG,QAAQ;IACxD;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO,CAAC;IAC7B;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,KAAK,CAAC;IAC3B;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,WAAW;IAChC;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACf;IAEA;;GAEC,GACD,KAAK;QACH,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD;IACb;IAEA;;GAEC,GACD,KAAK,CAAC,MAAqB,QAAgB;QACzC,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,GAAG,CAAC,QAAQ;IACjC;IAEA;;GAEC,GACD,UAAU,CAAC,MAAqB,QAAgB;QAC9C,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,QAAQ,CAAC,QAAQ;IACtC;IAEA;;GAEC,GACD,UAAU,CAAC,OAAsB;QAC/B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,QAAQ,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACrC;IAEA;;GAEC,GACD,SAAS,CAAC,OAAsB;QAC9B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,OAAO,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACpC;IAEA;;GAEC,GACD,MAAM,CAAC,OAAsB,OAAsB;QACjD,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,IAAI,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,QAAQ;IACzC;AACF;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;;uCAOe"}}, {"offset": {"line": 1679, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/leagues/components/league-table.tsx"], "sourcesContent": ["// APISportsGame CMS - League Table Component\n// Displays leagues in a data table với advanced features\n\n'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Table,\n  Card,\n  Space,\n  Button,\n  Input,\n  Select,\n  Tag,\n  Avatar,\n  Tooltip,\n  Modal,\n  Typography,\n  Row,\n  Col,\n  Dropdown,\n  message,\n  Image,\n} from 'antd';\nimport {\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  SyncOutlined,\n  PlusOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  MoreOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { League, LeagueFilters, LeagueTableProps, getLeagueStatus } from '../types';\nimport { dateUtils } from '@/shared/utils/date';\n\nconst { Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\n// ============================================================================\n// LEAGUE TABLE COMPONENT\n// ============================================================================\n\nexport const LeagueTable: React.FC<LeagueTableProps> = ({\n  leagues,\n  loading = false,\n  onEdit,\n  onDelete,\n  onAdd,\n  onRefresh,\n  onExport,\n  onSync,\n  pagination,\n  filters = {},\n  onFiltersChange,\n}) => {\n  // ========================================================================\n  // STATE\n  // ========================================================================\n\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [searchText, setSearchText] = useState(filters.search || '');\n\n  // ========================================================================\n  // COLUMN DEFINITIONS\n  // ========================================================================\n\n  const columns = [\n    {\n      title: 'League',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record: League) => (\n        <Space>\n          <Avatar \n            size=\"small\" \n            src={record.logo} \n            icon={<TrophyOutlined />}\n          />\n          <div>\n            <div className=\"font-medium\">{name}</div>\n            <Text type=\"secondary\" className=\"text-sm\">\n              Season {record.season}\n            </Text>\n          </div>\n        </Space>\n      ),\n      sorter: true,\n    },\n    {\n      title: 'Country',\n      dataIndex: 'country',\n      key: 'country',\n      render: (country: string, record: League) => (\n        <Space>\n          {record.flag && (\n            <Image\n              src={record.flag}\n              alt={country}\n              width={20}\n              height={15}\n              preview={false}\n            />\n          )}\n          <Text>{country}</Text>\n        </Space>\n      ),\n      filters: [\n        { text: 'England', value: 'England' },\n        { text: 'Spain', value: 'Spain' },\n        { text: 'Germany', value: 'Germany' },\n        { text: 'Italy', value: 'Italy' },\n        { text: 'France', value: 'France' },\n      ],\n      sorter: true,\n    },\n    {\n      title: 'Season',\n      dataIndex: 'season',\n      key: 'season',\n      render: (season: number) => (\n        <Tag color=\"blue\">{season}</Tag>\n      ),\n      sorter: true,\n    },\n    {\n      title: 'Status',\n      key: 'status',\n      render: (_, record: League) => {\n        const status = getLeagueStatus(record);\n        const statusConfig = {\n          active: { color: 'success', text: 'Active' },\n          upcoming: { color: 'processing', text: 'Upcoming' },\n          finished: { color: 'default', text: 'Finished' },\n          inactive: { color: 'error', text: 'Inactive' },\n        };\n        \n        const config = statusConfig[status];\n        \n        return (\n          <Space direction=\"vertical\" size=\"small\">\n            <Tag color={config.color}>{config.text}</Tag>\n            {record.current && (\n              <Tag color=\"gold\" size=\"small\">Current</Tag>\n            )}\n          </Space>\n        );\n      },\n      filters: [\n        { text: 'Active', value: 'active' },\n        { text: 'Upcoming', value: 'upcoming' },\n        { text: 'Finished', value: 'finished' },\n        { text: 'Inactive', value: 'inactive' },\n      ],\n    },\n    {\n      title: 'Duration',\n      key: 'duration',\n      render: (_, record: League) => (\n        <Space direction=\"vertical\" size=\"small\">\n          <Text type=\"secondary\" className=\"text-sm\">\n            <CalendarOutlined /> {dateUtils.formatDisplay(record.start)}\n          </Text>\n          <Text type=\"secondary\" className=\"text-sm\">\n            to {dateUtils.formatDisplay(record.end)}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: 'Coverage',\n      key: 'coverage',\n      render: (_, record: League) => {\n        const coverageItems = [];\n        if (record.coverage.fixtures.events) coverageItems.push('Events');\n        if (record.coverage.standings) coverageItems.push('Standings');\n        if (record.coverage.players) coverageItems.push('Players');\n        \n        return (\n          <Space wrap>\n            {coverageItems.slice(0, 2).map(item => (\n              <Tag key={item} size=\"small\">{item}</Tag>\n            ))}\n            {coverageItems.length > 2 && (\n              <Tooltip title={coverageItems.slice(2).join(', ')}>\n                <Tag size=\"small\">+{coverageItems.length - 2}</Tag>\n              </Tooltip>\n            )}\n          </Space>\n        );\n      },\n    },\n    {\n      title: 'Created',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      render: (date: string) => (\n        <Text type=\"secondary\" className=\"text-sm\">\n          {dateUtils.formatDisplay(date)}\n        </Text>\n      ),\n      sorter: true,\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      width: 120,\n      render: (_, record: League) => (\n        <Space>\n          <Tooltip title=\"View Details\">\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"Edit\">\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => onEdit?.(record)}\n            />\n          </Tooltip>\n          <Dropdown\n            menu={{\n              items: [\n                {\n                  key: 'delete',\n                  label: 'Delete',\n                  icon: <DeleteOutlined />,\n                  danger: true,\n                  onClick: () => handleDelete(record),\n                },\n              ],\n            }}\n            trigger={['click']}\n          >\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<MoreOutlined />}\n            />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleView = (league: League) => {\n    // Navigate to league details page\n    console.log('View league:', league);\n  };\n\n  const handleDelete = (league: League) => {\n    Modal.confirm({\n      title: 'Delete League',\n      content: `Are you sure you want to delete league \"${league.name}\"?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        onDelete?.(league);\n      },\n    });\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n    onFiltersChange?.({ ...filters, search: value });\n  };\n\n  const handleFilterChange = (key: string, value: any) => {\n    onFiltersChange?.({ ...filters, [key]: value });\n  };\n\n  const handleBulkDelete = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('Please select leagues to delete');\n      return;\n    }\n\n    Modal.confirm({\n      title: 'Delete Selected Leagues',\n      content: `Are you sure you want to delete ${selectedRowKeys.length} selected leagues?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        // Handle bulk delete\n        message.success(`${selectedRowKeys.length} leagues deleted successfully`);\n        setSelectedRowKeys([]);\n      },\n    });\n  };\n\n  // ========================================================================\n  // ROW SELECTION\n  // ========================================================================\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: League) => ({\n      disabled: false,\n      name: record.name,\n    }),\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Card>\n      {/* Table Header */}\n      <div className=\"mb-4\">\n        <Row gutter={16} align=\"middle\">\n          <Col flex=\"auto\">\n            <Space>\n              <Search\n                placeholder=\"Search leagues...\"\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                onSearch={handleSearch}\n                style={{ width: 300 }}\n                allowClear\n              />\n              <Select\n                placeholder=\"Country\"\n                value={filters.country}\n                onChange={(value) => handleFilterChange('country', value)}\n                style={{ width: 150 }}\n                allowClear\n              >\n                <Option value=\"England\">England</Option>\n                <Option value=\"Spain\">Spain</Option>\n                <Option value=\"Germany\">Germany</Option>\n                <Option value=\"Italy\">Italy</Option>\n                <Option value=\"France\">France</Option>\n              </Select>\n              <Select\n                placeholder=\"Season\"\n                value={filters.season}\n                onChange={(value) => handleFilterChange('season', value)}\n                style={{ width: 120 }}\n                allowClear\n              >\n                {Array.from({ length: 5 }, (_, i) => {\n                  const year = new Date().getFullYear() - i;\n                  return (\n                    <Option key={year} value={year}>{year}</Option>\n                  );\n                })}\n              </Select>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {selectedRowKeys.length > 0 && (\n                <Button\n                  danger\n                  icon={<DeleteOutlined />}\n                  onClick={handleBulkDelete}\n                >\n                  Delete ({selectedRowKeys.length})\n                </Button>\n              )}\n              <Button\n                icon={<SyncOutlined />}\n                onClick={onSync}\n                loading={loading}\n              >\n                Sync\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={onExport}\n              >\n                Export\n              </Button>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={onRefresh}\n                loading={loading}\n              >\n                Refresh\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={onAdd}\n              >\n                Add League\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </div>\n\n      {/* Data Table */}\n      <Table\n        columns={columns}\n        dataSource={leagues}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={pagination}\n        rowSelection={rowSelection}\n        scroll={{ x: 1200 }}\n        size=\"middle\"\n      />\n    </Card>\n  );\n};\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,yDAAyD;;;;;AAIzD;AAkCA;AACA;AAlCA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAlBA;AAAA;AAkBA;AAgBA;AAlCA;AAAA;AAkBA;AAAA;AAlBA;AAkBA;AAAA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAlBA;AAHA;;;;;;;AAuCA,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,gLAAA,CAAA,QAAK;AACxB,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAMlB,MAAM,cAA0C,CAAC,EACtD,OAAO,EACP,UAAU,KAAK,EACf,MAAM,EACN,QAAQ,EACR,KAAK,EACL,SAAS,EACT,QAAQ,EACR,MAAM,EACN,UAAU,EACV,UAAU,CAAC,CAAC,EACZ,eAAe,EAChB;IACC,2EAA2E;IAC3E,QAAQ;IACR,2EAA2E;IAE3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,MAAM,IAAI;IAE/D,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,UAAU;QACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAc,uBACrB,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,kLAAA,CAAA,SAAM;4BACL,MAAK;4BACL,KAAK,OAAO,IAAI;4BAChB,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;;;;;;sCAEvB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAe;;;;;;8CAC9B,8OAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAAU;wCACjC,OAAO,MAAM;;;;;;;;;;;;;;;;;;;YAK7B,QAAQ;QACV;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,SAAiB,uBACxB,8OAAC,gMAAA,CAAA,QAAK;;wBACH,OAAO,IAAI,kBACV,8OAAC,gLAAA,CAAA,QAAK;4BACJ,KAAK,OAAO,IAAI;4BAChB,KAAK;4BACL,OAAO;4BACP,QAAQ;4BACR,SAAS;;;;;;sCAGb,8OAAC;sCAAM;;;;;;;;;;;;YAGX,SAAS;gBACP;oBAAE,MAAM;oBAAW,OAAO;gBAAU;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAQ;gBAChC;oBAAE,MAAM;oBAAW,OAAO;gBAAU;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAQ;gBAChC;oBAAE,MAAM;oBAAU,OAAO;gBAAS;aACnC;YACD,QAAQ;QACV;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,uBACP,8OAAC,4KAAA,CAAA,MAAG;oBAAC,OAAM;8BAAQ;;;;;;YAErB,QAAQ;QACV;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,MAAM,SAAS,CAAA,GAAA,2IAAA,CAAA,kBAAe,AAAD,EAAE;gBAC/B,MAAM,eAAe;oBACnB,QAAQ;wBAAE,OAAO;wBAAW,MAAM;oBAAS;oBAC3C,UAAU;wBAAE,OAAO;wBAAc,MAAM;oBAAW;oBAClD,UAAU;wBAAE,OAAO;wBAAW,MAAM;oBAAW;oBAC/C,UAAU;wBAAE,OAAO;wBAAS,MAAM;oBAAW;gBAC/C;gBAEA,MAAM,SAAS,YAAY,CAAC,OAAO;gBAEnC,qBACE,8OAAC,gMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAW,MAAK;;sCAC/B,8OAAC,4KAAA,CAAA,MAAG;4BAAC,OAAO,OAAO,KAAK;sCAAG,OAAO,IAAI;;;;;;wBACrC,OAAO,OAAO,kBACb,8OAAC,4KAAA,CAAA,MAAG;4BAAC,OAAM;4BAAO,MAAK;sCAAQ;;;;;;;;;;;;YAIvC;YACA,SAAS;gBACP;oBAAE,MAAM;oBAAU,OAAO;gBAAS;gBAClC;oBAAE,MAAM;oBAAY,OAAO;gBAAW;gBACtC;oBAAE,MAAM;oBAAY,OAAO;gBAAW;gBACtC;oBAAE,MAAM;oBAAY,OAAO;gBAAW;aACvC;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC,gMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAW,MAAK;;sCAC/B,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;8CAC/B,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCAAG;gCAAE,8IAAA,CAAA,YAAS,CAAC,aAAa,CAAC,OAAO,KAAK;;;;;;;sCAE5D,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAU;gCACrC,8IAAA,CAAA,YAAS,CAAC,aAAa,CAAC,OAAO,GAAG;;;;;;;;;;;;;QAI9C;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,MAAM,gBAAgB,EAAE;gBACxB,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,cAAc,IAAI,CAAC;gBACxD,IAAI,OAAO,QAAQ,CAAC,SAAS,EAAE,cAAc,IAAI,CAAC;gBAClD,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,cAAc,IAAI,CAAC;gBAEhD,qBACE,8OAAC,gMAAA,CAAA,QAAK;oBAAC,IAAI;;wBACR,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,qBAC7B,8OAAC,4KAAA,CAAA,MAAG;gCAAY,MAAK;0CAAS;+BAApB;;;;;wBAEX,cAAc,MAAM,GAAG,mBACtB,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAO,cAAc,KAAK,CAAC,GAAG,IAAI,CAAC;sCAC1C,cAAA,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAK;;oCAAQ;oCAAE,cAAc,MAAM,GAAG;;;;;;;;;;;;;;;;;;YAKrD;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,8OAAC;oBAAK,MAAK;oBAAY,WAAU;8BAC9B,8IAAA,CAAA,YAAS,CAAC,aAAa,CAAC;;;;;;YAG7B,QAAQ;QACV;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;gCAClB,SAAS,IAAM,WAAW;;;;;;;;;;;sCAG9B,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,SAAS;;;;;;;;;;;sCAG5B,8OAAC,sLAAA,CAAA,WAAQ;4BACP,MAAM;gCACJ,OAAO;oCACL;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,QAAQ;wCACR,SAAS,IAAM,aAAa;oCAC9B;iCACD;4BACH;4BACA,SAAS;gCAAC;6BAAQ;sCAElB,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;QAK7B;KACD;IAED,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,aAAa,CAAC;QAClB,kCAAkC;QAClC,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,MAAM,eAAe,CAAC;QACpB,gLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,wCAAwC,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;YACnE,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,WAAW;YACb;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,kBAAkB;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAChD;IAEA,MAAM,qBAAqB,CAAC,KAAa;QACvC,kBAAkB;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;IAC/C;IAEA,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,gLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,gCAAgC,EAAE,gBAAgB,MAAM,CAAC,kBAAkB,CAAC;YACtF,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,qBAAqB;gBACrB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,6BAA6B,CAAC;gBACxE,mBAAmB,EAAE;YACvB;QACF;IACF;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,MAAM,eAAe;QACnB;QACA,UAAU;QACV,kBAAkB,CAAC,SAAmB,CAAC;gBACrC,UAAU;gBACV,MAAM,OAAO,IAAI;YACnB,CAAC;IACH;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BAEH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;oBAAI,OAAM;;sCACrB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,MAAK;sCACR,cAAA,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC;wCACC,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;;;;;;kDAEZ,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,OAAO,QAAQ,OAAO;wCACtB,UAAU,CAAC,QAAU,mBAAmB,WAAW;wCACnD,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;kDAEzB,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,QAAU,mBAAmB,UAAU;wCAClD,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;kDAET,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,CAAC,GAAG;4CAC7B,MAAM,OAAO,IAAI,OAAO,WAAW,KAAK;4CACxC,qBACE,8OAAC;gDAAkB,OAAO;0DAAO;+CAApB;;;;;wCAEjB;;;;;;;;;;;;;;;;;sCAIN,8OAAC,4KAAA,CAAA,MAAG;sCACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;oCACH,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAM;wCACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS;;4CACV;4CACU,gBAAgB,MAAM;4CAAC;;;;;;;kDAGpC,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;wCACT,SAAS;kDACV;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS;kDACV;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS;wCACT,SAAS;kDACV;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,QAAQ;oBAAE,GAAG;gBAAK;gBAClB,MAAK;;;;;;;;;;;;AAIb"}}, {"offset": {"line": 2447, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2453, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/leagues/components/league-form.tsx"], "sourcesContent": ["// APISportsGame CMS - League Form Component\n// Form for creating và editing leagues\n\n'use client';\n\nimport React, { useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Space,\n  Typography,\n  Divider,\n  Upload,\n  message,\n  Collapse,\n} from 'antd';\nimport {\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  TrophyOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { League, LeagueFormProps, LEAGUE_COUNTRIES, LEAGUE_SEASONS } from '../types';\nimport { dateUtils } from '@/shared/utils/date';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\nconst { RangePicker } = DatePicker;\nconst { Panel } = Collapse;\n\n// ============================================================================\n// LEAGUE FORM COMPONENT\n// ============================================================================\n\nexport const LeagueForm: React.FC<LeagueFormProps> = ({\n  mode,\n  initialValues,\n  loading = false,\n  onSubmit,\n  onCancel,\n}) => {\n  // ========================================================================\n  // FORM SETUP\n  // ========================================================================\n\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    if (initialValues) {\n      // Convert dates for form\n      const formValues = {\n        ...initialValues,\n        dateRange: initialValues.start && initialValues.end \n          ? [dateUtils.parseForForm(initialValues.start), dateUtils.parseForForm(initialValues.end)]\n          : null,\n      };\n      form.setFieldsValue(formValues);\n    }\n  }, [initialValues, form]);\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleSubmit = async (values: any) => {\n    try {\n      // Process form values\n      const processedValues = {\n        ...values,\n        start: values.dateRange?.[0]?.toISOString(),\n        end: values.dateRange?.[1]?.toISOString(),\n      };\n\n      // Remove dateRange field\n      delete processedValues.dateRange;\n\n      await onSubmit(processedValues);\n      \n      if (mode === 'create') {\n        form.resetFields();\n        message.success('League created successfully');\n      } else {\n        message.success('League updated successfully');\n      }\n    } catch (error) {\n      console.error('Form submission error:', error);\n    }\n  };\n\n  const handleCancel = () => {\n    form.resetFields();\n    onCancel();\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3} className=\"mb-2\">\n          <TrophyOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New League' : 'Edit League'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new league to the system'\n            : 'Update league information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        disabled={loading}\n        size=\"large\"\n      >\n        {/* Basic Information */}\n        <Card size=\"small\" className=\"mb-4\">\n          <Title level={5} className=\"mb-4\">Basic Information</Title>\n          \n          <Row gutter={16}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"League Name\"\n                rules={[\n                  { required: true, message: 'Please enter league name' },\n                  { min: 2, message: 'Name must be at least 2 characters' },\n                ]}\n              >\n                <Input placeholder=\"e.g., Premier League\" />\n              </Form.Item>\n            </Col>\n            \n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"country\"\n                label=\"Country\"\n                rules={[{ required: true, message: 'Please select country' }]}\n              >\n                <Select\n                  placeholder=\"Select country\"\n                  showSearch\n                  filterOption={(input, option) =>\n                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                  }\n                >\n                  {LEAGUE_COUNTRIES.map(country => (\n                    <Option key={country} value={country}>{country}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"season\"\n                label=\"Season\"\n                rules={[{ required: true, message: 'Please select season' }]}\n              >\n                <Select placeholder=\"Select season\">\n                  {LEAGUE_SEASONS.map(season => (\n                    <Option key={season} value={season}>{season}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            \n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"dateRange\"\n                label=\"Season Duration\"\n                rules={[{ required: true, message: 'Please select season dates' }]}\n              >\n                <RangePicker \n                  style={{ width: '100%' }}\n                  placeholder={['Start Date', 'End Date']}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"logo\"\n                label=\"League Logo URL\"\n              >\n                <Input placeholder=\"https://example.com/logo.png\" />\n              </Form.Item>\n            </Col>\n            \n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"flag\"\n                label=\"Country Flag URL\"\n              >\n                <Input placeholder=\"https://example.com/flag.png\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"current\"\n            label=\"Current Season\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"Yes\" unCheckedChildren=\"No\" />\n          </Form.Item>\n        </Card>\n\n        {/* Coverage Settings */}\n        <Card size=\"small\" className=\"mb-4\">\n          <Title level={5} className=\"mb-4\">\n            <SettingOutlined className=\"mr-2\" />\n            Coverage Settings\n          </Title>\n          \n          <Collapse ghost>\n            <Panel header=\"Fixture Coverage\" key=\"fixtures\">\n              <Row gutter={16}>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name={['coverage', 'fixtures', 'events']}\n                    label=\"Events\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name={['coverage', 'fixtures', 'lineups']}\n                    label=\"Lineups\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name={['coverage', 'fixtures', 'statistics_fixtures']}\n                    label=\"Fixture Statistics\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name={['coverage', 'fixtures', 'statistics_players']}\n                    label=\"Player Statistics\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n            </Panel>\n            \n            <Panel header=\"Additional Coverage\" key=\"additional\">\n              <Row gutter={16}>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'standings']}\n                    label=\"Standings\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'players']}\n                    label=\"Players\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'top_scorers']}\n                    label=\"Top Scorers\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'top_assists']}\n                    label=\"Top Assists\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'top_cards']}\n                    label=\"Top Cards\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'injuries']}\n                    label=\"Injuries\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'predictions']}\n                    label=\"Predictions\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name={['coverage', 'odds']}\n                    label=\"Odds\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch size=\"small\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n            </Panel>\n          </Collapse>\n        </Card>\n\n        {/* Form Actions */}\n        <div className=\"flex justify-end\">\n          <Space>\n            <Button\n              size=\"large\"\n              icon={<CloseOutlined />}\n              onClick={handleCancel}\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              icon={<SaveOutlined />}\n              htmlType=\"submit\"\n              loading={loading}\n            >\n              {mode === 'create' ? 'Create League' : 'Update League'}\n            </Button>\n          </Space>\n        </div>\n      </Form>\n    </Card>\n  );\n};\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,uCAAuC;;;;;AAIvC;AAyBA;AACA;AAzBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AAzBA;AAAA;AAiBA;AAjBA;AAAA;AAAA;AAiBA;AAjBA;AAAA;AAiBA;AAAA;AApBA;;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,WAAW,EAAE,GAAG,8LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,KAAK,EAAE,GAAG,sLAAA,CAAA,WAAQ;AAMnB,MAAM,aAAwC,CAAC,EACpD,IAAI,EACJ,aAAa,EACb,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACT;IACC,2EAA2E;IAC3E,aAAa;IACb,2EAA2E;IAE3E,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,yBAAyB;YACzB,MAAM,aAAa;gBACjB,GAAG,aAAa;gBAChB,WAAW,cAAc,KAAK,IAAI,cAAc,GAAG,GAC/C;oBAAC,8IAAA,CAAA,YAAS,CAAC,YAAY,CAAC,cAAc,KAAK;oBAAG,8IAAA,CAAA,YAAS,CAAC,YAAY,CAAC,cAAc,GAAG;iBAAE,GACxF;YACN;YACA,KAAK,cAAc,CAAC;QACtB;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,sBAAsB;YACtB,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,OAAO,OAAO,SAAS,EAAE,CAAC,EAAE,EAAE;gBAC9B,KAAK,OAAO,SAAS,EAAE,CAAC,EAAE,EAAE;YAC9B;YAEA,yBAAyB;YACzB,OAAO,gBAAgB,SAAS;YAEhC,MAAM,SAAS;YAEf,IAAI,SAAS,UAAU;gBACrB,KAAK,WAAW;gBAChB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,eAAe;QACnB,KAAK,WAAW;QAChB;IACF;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;wBAAG,WAAU;;0CACzB,8OAAC,sNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;4BACzB,SAAS,WAAW,sBAAsB;;;;;;;kCAE7C,8OAAC;wBAAK,MAAK;kCACR,SAAS,WACN,mCACA;;;;;;;;;;;;0BAKR,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,UAAU;gBACV,UAAU;gBACV,MAAK;;kCAGL,8OAAC,8KAAA,CAAA,OAAI;wBAAC,MAAK;wBAAQ,WAAU;;0CAC3B,8OAAC;gCAAM,OAAO;gCAAG,WAAU;0CAAO;;;;;;0CAElC,8OAAC,4KAAA,CAAA,MAAG;gCAAC,QAAQ;;kDACX,8OAAC,4KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,OAAO;gDACL;oDAAE,UAAU;oDAAM,SAAS;gDAA2B;gDACtD;oDAAE,KAAK;oDAAG,SAAS;gDAAqC;6CACzD;sDAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;gDAAC,aAAY;;;;;;;;;;;;;;;;kDAIvB,8OAAC,4KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAAwB;6CAAE;sDAE7D,cAAA,8OAAC,kLAAA,CAAA,SAAM;gDACL,aAAY;gDACZ,UAAU;gDACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;0DAGvE,2IAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAA,wBACpB,8OAAC;wDAAqB,OAAO;kEAAU;uDAA1B;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,QAAQ;;kDACX,8OAAC,4KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAAuB;6CAAE;sDAE5D,cAAA,8OAAC,kLAAA,CAAA,SAAM;gDAAC,aAAY;0DACjB,2IAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAA,uBAClB,8OAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;;;;;;;;;;;kDAMrB,8OAAC,4KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAA6B;6CAAE;sDAElE,cAAA,8OAAC;gDACC,OAAO;oDAAE,OAAO;gDAAO;gDACvB,aAAa;oDAAC;oDAAc;iDAAW;;;;;;;;;;;;;;;;;;;;;;0CAM/C,8OAAC,4KAAA,CAAA,MAAG;gCAAC,QAAQ;;kDACX,8OAAC,4KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;sDAEN,cAAA,8OAAC,gLAAA,CAAA,QAAK;gDAAC,aAAY;;;;;;;;;;;;;;;;kDAIvB,8OAAC,4KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;sDAEN,cAAA,8OAAC,gLAAA,CAAA,QAAK;gDAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;0CAKzB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,eAAc;0CAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;oCAAC,iBAAgB;oCAAM,mBAAkB;;;;;;;;;;;;;;;;;kCAKpD,8OAAC,8KAAA,CAAA,OAAI;wBAAC,MAAK;wBAAQ,WAAU;;0CAC3B,8OAAC;gCAAM,OAAO;gCAAG,WAAU;;kDACzB,8OAAC,wNAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAItC,8OAAC,sLAAA,CAAA,WAAQ;gCAAC,KAAK;;kDACb,8OAAC;wCAAM,QAAO;kDACZ,cAAA,8OAAC,4KAAA,CAAA,MAAG;4CAAC,QAAQ;;8DACX,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;4DAAY;yDAAS;wDACxC,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;4DAAY;yDAAU;wDACzC,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;4DAAY;yDAAsB;wDACrD,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;4DAAY;yDAAqB;wDACpD,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;uCAnCgB;;;;;kDAyCrC,8OAAC;wCAAM,QAAO;kDACZ,cAAA,8OAAC,4KAAA,CAAA,MAAG;4CAAC,QAAQ;;8DACX,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAY;wDAC/B,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAU;wDAC7B,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAc;wDACjC,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAc;wDACjC,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAY;wDAC/B,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAW;wDAC9B,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAc;wDACjC,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;8DAGjB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,IAAI;oDAAI,IAAI;8DACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wDACR,MAAM;4DAAC;4DAAY;yDAAO;wDAC1B,OAAM;wDACN,eAAc;kEAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4DAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;uCAvEmB;;;;;;;;;;;;;;;;;kCAgF5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACpB,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAK;oCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,UAAS;oCACT,SAAS;8CAER,SAAS,WAAW,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD"}}, {"offset": {"line": 3297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3303, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/leagues/page.tsx"], "sourcesContent": ["// APISportsGame CMS - League Management Page\n// Dashboard page for managing leagues\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  Space,\n  Button,\n  Modal,\n  message,\n  Tabs,\n} from 'antd';\nimport {\n  TrophyOutlined,\n  GlobalOutlined,\n  CalendarOutlined,\n  SyncOutlined,\n  PlusOutlined,\n  BarChartOutlined,\n} from '@ant-design/icons';\nimport { DashboardLayout } from '@/components/layouts/dashboard-layout';\nimport { LeagueTable } from '@/modules/leagues/components/league-table';\nimport { LeagueForm } from '@/modules/leagues/components/league-form';\nimport { usePermissions } from '@/stores/auth-store';\nimport { League, LeagueFilters } from '@/modules/leagues/types';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// MOCK DATA (Replace với real API calls)\n// ============================================================================\n\nconst mockLeagues: League[] = [\n  {\n    id: 1,\n    name: 'Premier League',\n    country: 'England',\n    logo: 'https://media.api-sports.io/football/leagues/39.png',\n    flag: 'https://media.api-sports.io/flags/gb.svg',\n    season: 2024,\n    start: '2024-08-17',\n    end: '2025-05-25',\n    current: true,\n    coverage: {\n      fixtures: {\n        events: true,\n        lineups: true,\n        statistics_fixtures: true,\n        statistics_players: true,\n      },\n      standings: true,\n      players: true,\n      top_scorers: true,\n      top_assists: true,\n      top_cards: true,\n      injuries: true,\n      predictions: true,\n      odds: true,\n    },\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-15'),\n  },\n  {\n    id: 2,\n    name: 'La Liga',\n    country: 'Spain',\n    logo: 'https://media.api-sports.io/football/leagues/140.png',\n    flag: 'https://media.api-sports.io/flags/es.svg',\n    season: 2024,\n    start: '2024-08-18',\n    end: '2025-05-25',\n    current: true,\n    coverage: {\n      fixtures: {\n        events: true,\n        lineups: true,\n        statistics_fixtures: true,\n        statistics_players: false,\n      },\n      standings: true,\n      players: true,\n      top_scorers: true,\n      top_assists: false,\n      top_cards: false,\n      injuries: false,\n      predictions: true,\n      odds: true,\n    },\n    createdAt: new Date('2024-01-12'),\n    updatedAt: new Date('2024-01-15'),\n  },\n];\n\nconst mockStats = {\n  total: 25,\n  active: 18,\n  current: 12,\n  byCountry: {\n    England: 3,\n    Spain: 2,\n    Germany: 3,\n    Italy: 2,\n    France: 2,\n  },\n  bySeason: {\n    '2024': 15,\n    '2023': 8,\n    '2022': 2,\n  },\n};\n\n// ============================================================================\n// LEAGUE MANAGEMENT PAGE\n// ============================================================================\n\nexport default function LeagueManagementPage() {\n  // ========================================================================\n  // STATE\n  // ========================================================================\n\n  const [loading, setLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState<League | null>(null);\n  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');\n  const [activeTab, setActiveTab] = useState('all');\n\n  // Filters và pagination\n  const [filters, setFilters] = useState<LeagueFilters>({});\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockLeagues.length,\n  });\n\n  // ========================================================================\n  // PERMISSIONS\n  // ========================================================================\n\n  const permissions = usePermissions();\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    // Load initial data\n    handleRefresh();\n  }, []);\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleRefresh = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      message.success('Leagues refreshed successfully');\n    } catch (error) {\n      message.error('Failed to refresh leagues');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddLeague = () => {\n    setSelectedLeague(null);\n    setFormMode('create');\n    setShowForm(true);\n  };\n\n  const handleEditLeague = (league: League) => {\n    setSelectedLeague(league);\n    setFormMode('edit');\n    setShowForm(true);\n  };\n\n  const handleDeleteLeague = async (league: League) => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      message.success(`League \"${league.name}\" deleted successfully`);\n      handleRefresh();\n    } catch (error) {\n      message.error('Failed to delete league');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFormSubmit = async (values: any) => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      if (formMode === 'create') {\n        message.success('League created successfully');\n      } else {\n        message.success('League updated successfully');\n      }\n      \n      setShowForm(false);\n      handleRefresh();\n    } catch (error) {\n      message.error(`Failed to ${formMode} league`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSync = async () => {\n    setLoading(true);\n    try {\n      // Simulate sync operation\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      message.success('Leagues synchronized successfully');\n      handleRefresh();\n    } catch (error) {\n      message.error('Failed to sync leagues');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleExport = () => {\n    message.info('Export functionality will be implemented');\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  if (!permissions.canManageLeagues) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <Text type=\"secondary\">You don't have permission to manage leagues.</Text>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6\">\n        {/* Page Header */}\n        <div className=\"mb-6\">\n          <Title level={2} className=\"mb-2\">\n            <TrophyOutlined className=\"mr-3\" />\n            League Management\n          </Title>\n          <Text type=\"secondary\">\n            Manage football leagues, seasons, and coverage settings\n          </Text>\n        </div>\n\n        {/* Statistics Cards */}\n        <Row gutter={16} className=\"mb-6\">\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total Leagues\"\n                value={mockStats.total}\n                prefix={<TrophyOutlined />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Active Leagues\"\n                value={mockStats.active}\n                prefix={<CalendarOutlined />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Current Season\"\n                value={mockStats.current}\n                prefix={<GlobalOutlined />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Countries\"\n                value={Object.keys(mockStats.byCountry).length}\n                prefix={<BarChartOutlined />}\n                valueStyle={{ color: '#fa8c16' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n\n        {/* League Management Tabs */}\n        <Card>\n          <Tabs\n            activeKey={activeTab}\n            onChange={setActiveTab}\n            tabBarExtraContent={\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleAddLeague}\n              >\n                Add League\n              </Button>\n            }\n            items={[\n              {\n                key: 'all',\n                label: (\n                  <Space>\n                    <TrophyOutlined />\n                    All Leagues ({mockStats.total})\n                  </Space>\n                ),\n                children: (\n                  <LeagueTable\n                    leagues={mockLeagues}\n                    loading={loading}\n                    onEdit={handleEditLeague}\n                    onDelete={handleDeleteLeague}\n                    onAdd={handleAddLeague}\n                    onRefresh={handleRefresh}\n                    onExport={handleExport}\n                    onSync={handleSync}\n                    pagination={{\n                      ...pagination,\n                      onChange: (page, pageSize) => {\n                        setPagination({ ...pagination, current: page, pageSize });\n                      },\n                    }}\n                    filters={filters}\n                    onFiltersChange={setFilters}\n                  />\n                ),\n              },\n              {\n                key: 'active',\n                label: (\n                  <Space>\n                    <CalendarOutlined />\n                    Active Leagues ({mockStats.active})\n                  </Space>\n                ),\n                children: (\n                  <LeagueTable\n                    leagues={mockLeagues.filter(league => league.current)}\n                    loading={loading}\n                    onEdit={handleEditLeague}\n                    onDelete={handleDeleteLeague}\n                    onAdd={handleAddLeague}\n                    onRefresh={handleRefresh}\n                    onExport={handleExport}\n                    onSync={handleSync}\n                    pagination={{\n                      ...pagination,\n                      onChange: (page, pageSize) => {\n                        setPagination({ ...pagination, current: page, pageSize });\n                      },\n                    }}\n                    filters={filters}\n                    onFiltersChange={setFilters}\n                  />\n                ),\n              },\n            ]}\n          />\n        </Card>\n\n        {/* League Form Modal */}\n        <Modal\n          title={null}\n          open={showForm}\n          onCancel={() => setShowForm(false)}\n          footer={null}\n          width={900}\n          destroyOnHidden\n        >\n          <LeagueForm\n            mode={formMode}\n            initialValues={selectedLeague}\n            loading={loading}\n            onSubmit={handleFormSubmit}\n            onCancel={() => setShowForm(false)}\n          />\n        </Modal>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,sCAAsC;;;;;AAItC;AAqBA;AACA;AACA;AACA;AAvBA;AAAA;AAYA;AAZA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAZA;AAAA;AAYA;AAZA;AAAA;AAHA;;;;;;;;;AA6BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,+EAA+E;AAC/E,yCAAyC;AACzC,+EAA+E;AAE/E,MAAM,cAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK;QACL,SAAS;QACT,UAAU;YACR,UAAU;gBACR,QAAQ;gBACR,SAAS;gBACT,qBAAqB;gBACrB,oBAAoB;YACtB;YACA,WAAW;YACX,SAAS;YACT,aAAa;YACb,aAAa;YACb,WAAW;YACX,UAAU;YACV,aAAa;YACb,MAAM;QACR;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK;QACL,SAAS;QACT,UAAU;YACR,UAAU;gBACR,QAAQ;gBACR,SAAS;gBACT,qBAAqB;gBACrB,oBAAoB;YACtB;YACA,WAAW;YACX,SAAS;YACT,aAAa;YACb,aAAa;YACb,WAAW;YACX,UAAU;YACV,aAAa;YACb,MAAM;QACR;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,YAAY;IAChB,OAAO;IACP,QAAQ;IACR,SAAS;IACT,WAAW;QACT,SAAS;QACT,OAAO;QACP,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA,UAAU;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;AACF;AAMe,SAAS;IACtB,2EAA2E;IAC3E,QAAQ;IACR,2EAA2E;IAE3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,SAAS;QACT,UAAU;QACV,OAAO,YAAY,MAAM;IAC3B;IAEA,2EAA2E;IAC3E,cAAc;IACd,2EAA2E;IAE3E,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;QACpB;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,YAAY;QACZ,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,YAAY;QACZ,YAAY;IACd;IAEA,MAAM,qBAAqB,OAAO;QAChC,WAAW;QACX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,sBAAsB,CAAC;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,WAAW;QACX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,aAAa,UAAU;gBACzB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,YAAY;YACZ;QACF,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,OAAO,CAAC;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,IAAI,CAAC,YAAY,gBAAgB,EAAE;QACjC,qBACE,8OAAC,oJAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;;;;;IAI/B;IAEA,qBACE,8OAAC,oJAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,OAAO;4BAAG,WAAU;;8CACzB,8OAAC,sNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAAS;;;;;;;sCAGrC,8OAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;8BAMzB,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;oBAAI,WAAU;;sCACzB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,UAAU,KAAK;oCACtB,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;oCACvB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,UAAU,MAAM;oCACvB,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oCACzB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,UAAU,OAAO;oCACxB,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;oCACvB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,OAAO,IAAI,CAAC,UAAU,SAAS,EAAE,MAAM;oCAC9C,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oCACzB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC,8KAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,8KAAA,CAAA,OAAI;wBACH,WAAW;wBACX,UAAU;wBACV,kCACE,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS;sCACV;;;;;;wBAIH,OAAO;4BACL;gCACE,KAAK;gCACL,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCAAG;wCACJ,UAAU,KAAK;wCAAC;;;;;;;gCAGlC,wBACE,8OAAC,2JAAA,CAAA,cAAW;oCACV,SAAS;oCACT,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO;oCACP,WAAW;oCACX,UAAU;oCACV,QAAQ;oCACR,YAAY;wCACV,GAAG,UAAU;wCACb,UAAU,CAAC,MAAM;4CACf,cAAc;gDAAE,GAAG,UAAU;gDAAE,SAAS;gDAAM;4CAAS;wCACzD;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;4BAGvB;4BACA;gCACE,KAAK;gCACL,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAAG;wCACH,UAAU,MAAM;wCAAC;;;;;;;gCAGtC,wBACE,8OAAC,2JAAA,CAAA,cAAW;oCACV,SAAS,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO;oCACpD,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO;oCACP,WAAW;oCACX,UAAU;oCACV,QAAQ;oCACR,YAAY;wCACV,GAAG,UAAU;wCACb,UAAU,CAAC,MAAM;4CACf,cAAc;gDAAE,GAAG,UAAU;gDAAE,SAAS;gDAAM;4CAAS;wCACzD;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;4BAGvB;yBACD;;;;;;;;;;;8BAKL,8OAAC,gLAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,MAAM;oBACN,UAAU,IAAM,YAAY;oBAC5B,QAAQ;oBACR,OAAO;oBACP,eAAe;8BAEf,cAAA,8OAAC,0JAAA,CAAA,aAAU;wBACT,MAAM;wBACN,eAAe;wBACf,SAAS;wBACT,UAAU;wBACV,UAAU,IAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}, {"offset": {"line": 3872, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}