{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/constants/index.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Constants\n// Application-wide constants\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nexport const API_CONFIG = {\n  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',\n  TIMEOUT: 10000,\n  RETRY_ATTEMPTS: 3,\n} as const;\n\n// ============================================================================\n// PAGINATION DEFAULTS\n// ============================================================================\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_PAGE_SIZE: 10,\n  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],\n  MAX_PAGE_SIZE: 100,\n} as const;\n\n// ============================================================================\n// VALIDATION RULES\n// ============================================================================\n\nexport const VALIDATION = {\n  USERNAME: {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: 50,\n    PATTERN: /^[a-zA-Z0-9_]+$/,\n  },\n  PASSWORD: {\n    MIN_LENGTH: 6,\n    MAX_LENGTH: 100,\n  },\n  EMAIL: {\n    PATTERN: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  },\n  SEARCH: {\n    MIN_LENGTH: 2,\n    MAX_LENGTH: 100,\n  },\n} as const;\n\n// ============================================================================\n// UI CONSTANTS\n// ============================================================================\n\nexport const UI = {\n  DEBOUNCE_DELAY: 300,\n  ANIMATION_DURATION: 200,\n  NOTIFICATION_DURATION: 4000,\n  MODAL_WIDTH: {\n    SMALL: 400,\n    MEDIUM: 600,\n    LARGE: 800,\n    EXTRA_LARGE: 1000,\n  },\n} as const;\n\n// ============================================================================\n// STATUS COLORS\n// ============================================================================\n\nexport const STATUS_COLORS = {\n  SUCCESS: '#52c41a',\n  ERROR: '#ff4d4f',\n  WARNING: '#faad14',\n  INFO: '#1890ff',\n  PROCESSING: '#722ed1',\n  DEFAULT: '#d9d9d9',\n} as const;\n\n// ============================================================================\n// USER ROLES & TIERS\n// ============================================================================\n\nexport const USER_ROLES = {\n  ADMIN: 'admin',\n  EDITOR: 'editor',\n  MODERATOR: 'moderator',\n} as const;\n\nexport const USER_TIERS = {\n  FREE: 'free',\n  PREMIUM: 'premium',\n  ENTERPRISE: 'enterprise',\n} as const;\n\nexport const ROLE_COLORS = {\n  [USER_ROLES.ADMIN]: 'red',\n  [USER_ROLES.EDITOR]: 'blue',\n  [USER_ROLES.MODERATOR]: 'green',\n} as const;\n\nexport const TIER_COLORS = {\n  [USER_TIERS.FREE]: 'default',\n  [USER_TIERS.PREMIUM]: 'gold',\n  [USER_TIERS.ENTERPRISE]: 'purple',\n} as const;\n\n// ============================================================================\n// API LIMITS\n// ============================================================================\n\nexport const API_LIMITS = {\n  FREE_TIER: 1000,\n  PREMIUM_TIER: 50000,\n  ENTERPRISE_TIER: 200000,\n  UNLIMITED: null,\n} as const;\n\n// ============================================================================\n// DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// ROUTES\n// ============================================================================\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/auth/login',\n  DASHBOARD: '/dashboard',\n  USERS: '/dashboard/users',\n  LEAGUES: '/dashboard/leagues',\n  TEAMS: '/dashboard/teams',\n  FIXTURES: '/dashboard/fixtures',\n  ANALYTICS: '/dashboard/analytics',\n  SYNC: '/dashboard/sync',\n  PROFILE: '/dashboard/profile',\n  SETTINGS: '/dashboard/settings',\n} as const;\n\n// ============================================================================\n// LOCAL STORAGE KEYS\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  ACCESS_TOKEN: 'access_token',\n  REFRESH_TOKEN: 'refresh_token',\n  USER_PREFERENCES: 'user_preferences',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n} as const;\n\n// ============================================================================\n// ERROR MESSAGES\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied. Insufficient permissions.',\n  NOT_FOUND: 'The requested resource was not found.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  SERVER_ERROR: 'Internal server error. Please try again later.',\n  UNKNOWN_ERROR: 'An unknown error occurred.',\n} as const;\n\n// ============================================================================\n// SUCCESS MESSAGES\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  CREATED: 'Created successfully',\n  UPDATED: 'Updated successfully',\n  DELETED: 'Deleted successfully',\n  SAVED: 'Saved successfully',\n  SYNCED: 'Synced successfully',\n  EXPORTED: 'Exported successfully',\n} as const;\n\n// ============================================================================\n// QUERY KEYS\n// ============================================================================\n\nexport const QUERY_KEYS = {\n  AUTH: ['auth'],\n  USERS: ['users'],\n  LEAGUES: ['leagues'],\n  TEAMS: ['teams'],\n  FIXTURES: ['fixtures'],\n  DASHBOARD: ['dashboard'],\n  SYNC: ['sync'],\n} as const;\n\n// ============================================================================\n// PERMISSIONS\n// ============================================================================\n\nexport const PERMISSIONS = {\n  USERS: {\n    VIEW: 'users:view',\n    CREATE: 'users:create',\n    UPDATE: 'users:update',\n    DELETE: 'users:delete',\n  },\n  LEAGUES: {\n    VIEW: 'leagues:view',\n    CREATE: 'leagues:create',\n    UPDATE: 'leagues:update',\n    DELETE: 'leagues:delete',\n  },\n  TEAMS: {\n    VIEW: 'teams:view',\n    CREATE: 'teams:create',\n    UPDATE: 'teams:update',\n    DELETE: 'teams:delete',\n  },\n  FIXTURES: {\n    VIEW: 'fixtures:view',\n    CREATE: 'fixtures:create',\n    UPDATE: 'fixtures:update',\n    DELETE: 'fixtures:delete',\n  },\n  SYNC: {\n    VIEW: 'sync:view',\n    EXECUTE: 'sync:execute',\n  },\n  ANALYTICS: {\n    VIEW: 'analytics:view',\n  },\n} as const;\n\n// ============================================================================\n// EXPORT TYPES\n// ============================================================================\n\nexport type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];\nexport type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS];\nexport type Route = typeof ROUTES[keyof typeof ROUTES];\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS][keyof typeof PERMISSIONS[keyof typeof PERMISSIONS]];\n\n// ============================================================================\n// DEFAULT EXPORT\n// ============================================================================\n\nexport default {\n  API_CONFIG,\n  PAGINATION,\n  VALIDATION,\n  UI,\n  STATUS_COLORS,\n  USER_ROLES,\n  USER_TIERS,\n  ROLE_COLORS,\n  TIER_COLORS,\n  API_LIMITS,\n  DATE_FORMATS,\n  ROUTES,\n  STORAGE_KEYS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  QUERY_KEYS,\n  PERMISSIONS,\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,6BAA6B;AAE7B,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;;;;;;;;;;;;;;;;;;;;;AAExE,MAAM,aAAa;IACxB,UAAU,iEAAmC;IAC7C,SAAS;IACT,gBAAgB;AAClB;AAMO,MAAM,aAAa;IACxB,cAAc;IACd,mBAAmB;IACnB,mBAAmB;QAAC;QAAI;QAAI;QAAI;KAAI;IACpC,eAAe;AACjB;AAMO,MAAM,aAAa;IACxB,UAAU;QACR,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IACA,UAAU;QACR,YAAY;QACZ,YAAY;IACd;IACA,OAAO;QACL,SAAS;IACX;IACA,QAAQ;QACN,YAAY;QACZ,YAAY;IACd;AACF;AAMO,MAAM,KAAK;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,aAAa;QACX,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;AACF;AAMO,MAAM,gBAAgB;IAC3B,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,YAAY;IACZ,SAAS;AACX;AAMO,MAAM,aAAa;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,YAAY;AACd;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,KAAK,CAAC,EAAE;IACpB,CAAC,WAAW,MAAM,CAAC,EAAE;IACrB,CAAC,WAAW,SAAS,CAAC,EAAE;AAC1B;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,IAAI,CAAC,EAAE;IACnB,CAAC,WAAW,OAAO,CAAC,EAAE;IACtB,CAAC,WAAW,UAAU,CAAC,EAAE;AAC3B;AAMO,MAAM,aAAa;IACxB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,WAAW;AACb;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;AAMO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,WAAW;IACX,OAAO;IACP,SAAS;IACT,OAAO;IACP,UAAU;IACV,WAAW;IACX,MAAM;IACN,SAAS;IACT,UAAU;AACZ;AAMO,MAAM,eAAe;IAC1B,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,OAAO;IACP,UAAU;AACZ;AAMO,MAAM,iBAAiB;IAC5B,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,eAAe;AACjB;AAMO,MAAM,mBAAmB;IAC9B,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,UAAU;AACZ;AAMO,MAAM,aAAa;IACxB,MAAM;QAAC;KAAO;IACd,OAAO;QAAC;KAAQ;IAChB,SAAS;QAAC;KAAU;IACpB,OAAO;QAAC;KAAQ;IAChB,UAAU;QAAC;KAAW;IACtB,WAAW;QAAC;KAAY;IACxB,MAAM;QAAC;KAAO;AAChB;AAMO,MAAM,cAAc;IACzB,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,UAAU;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF;uCAee;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/api.ts"], "sourcesContent": ["// APISportsGame CMS - Shared API Utilities\n// Common API utilities và error handling\n\nimport { AxiosError } from 'axios';\n\n// ============================================================================\n// API ERROR HANDLING\n// ============================================================================\n\nexport interface ApiErrorResponse {\n  statusCode: number;\n  message: string | string[];\n  error: string;\n  timestamp: string;\n  path: string;\n}\n\nexport class ApiError extends Error {\n  public statusCode: number;\n  public response?: ApiErrorResponse;\n\n  constructor(error: AxiosError) {\n    const response = error.response?.data as ApiErrorResponse;\n    const message = Array.isArray(response?.message) \n      ? response.message.join(', ')\n      : response?.message || error.message || 'An error occurred';\n\n    super(message);\n    this.name = 'ApiError';\n    this.statusCode = error.response?.status || 500;\n    this.response = response;\n  }\n}\n\n// ============================================================================\n// API RESPONSE UTILITIES\n// ============================================================================\n\nexport const apiUtils = {\n  /**\n   * Handle API errors consistently\n   */\n  handleError: (error: unknown): ApiError => {\n    if (error instanceof AxiosError) {\n      return new ApiError(error);\n    }\n    \n    if (error instanceof Error) {\n      const apiError = new ApiError({} as AxiosError);\n      apiError.message = error.message;\n      return apiError;\n    }\n\n    const apiError = new ApiError({} as AxiosError);\n    apiError.message = 'Unknown error occurred';\n    return apiError;\n  },\n\n  /**\n   * Extract error message for display\n   */\n  getErrorMessage: (error: unknown): string => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.message;\n  },\n\n  /**\n   * Check if error is specific status code\n   */\n  isErrorStatus: (error: unknown, statusCode: number): boolean => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.statusCode === statusCode;\n  },\n\n  /**\n   * Check if error is unauthorized\n   */\n  isUnauthorized: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 401);\n  },\n\n  /**\n   * Check if error is forbidden\n   */\n  isForbidden: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 403);\n  },\n\n  /**\n   * Check if error is not found\n   */\n  isNotFound: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 404);\n  },\n\n  /**\n   * Check if error is validation error\n   */\n  isValidationError: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 400);\n  },\n\n  /**\n   * Format pagination params\n   */\n  formatPaginationParams: (page: number, limit: number) => ({\n    page,\n    limit,\n    offset: (page - 1) * limit,\n  }),\n\n  /**\n   * Format filter params (remove undefined values)\n   */\n  formatFilterParams: (filters: Record<string, any>) => {\n    const cleanFilters: Record<string, any> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        cleanFilters[key] = value;\n      }\n    });\n\n    return cleanFilters;\n  },\n\n  /**\n   * Build query string from params\n   */\n  buildQueryString: (params: Record<string, any>): string => {\n    const cleanParams = apiUtils.formatFilterParams(params);\n    const searchParams = new URLSearchParams();\n\n    Object.entries(cleanParams).forEach(([key, value]) => {\n      if (Array.isArray(value)) {\n        value.forEach(v => searchParams.append(key, String(v)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    });\n\n    return searchParams.toString();\n  },\n};\n\n// ============================================================================\n// PAGINATION UTILITIES\n// ============================================================================\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface PaginationMeta {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: PaginationMeta;\n}\n\nexport const paginationUtils = {\n  /**\n   * Calculate total pages\n   */\n  calculateTotalPages: (total: number, limit: number): number => {\n    return Math.ceil(total / limit);\n  },\n\n  /**\n   * Calculate offset from page and limit\n   */\n  calculateOffset: (page: number, limit: number): number => {\n    return (page - 1) * limit;\n  },\n\n  /**\n   * Get pagination info for display\n   */\n  getPaginationInfo: (meta: PaginationMeta) => {\n    const start = paginationUtils.calculateOffset(meta.page, meta.limit) + 1;\n    const end = Math.min(start + meta.limit - 1, meta.total);\n    \n    return {\n      start,\n      end,\n      total: meta.total,\n      hasNext: meta.page < meta.totalPages,\n      hasPrev: meta.page > 1,\n    };\n  },\n};\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport default apiUtils;\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,yCAAyC;;;;;;;AAEzC;;AAcO,MAAM,iBAAiB;IACrB,WAAmB;IACnB,SAA4B;IAEnC,YAAY,KAAiB,CAAE;QAC7B,MAAM,WAAW,MAAM,QAAQ,EAAE;QACjC,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,WACpC,SAAS,OAAO,CAAC,IAAI,CAAC,QACtB,UAAU,WAAW,MAAM,OAAO,IAAI;QAE1C,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG,MAAM,QAAQ,EAAE,UAAU;QAC5C,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAMO,MAAM,WAAW;IACtB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,iBAAiB,8IAAA,CAAA,aAAU,EAAE;YAC/B,OAAO,IAAI,SAAS;QACtB;QAEA,IAAI,iBAAiB,OAAO;YAC1B,MAAM,WAAW,IAAI,SAAS,CAAC;YAC/B,SAAS,OAAO,GAAG,MAAM,OAAO;YAChC,OAAO;QACT;QAEA,MAAM,WAAW,IAAI,SAAS,CAAC;QAC/B,SAAS,OAAO,GAAG;QACnB,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,OAAO;IACzB;IAEA;;GAEC,GACD,eAAe,CAAC,OAAgB;QAC9B,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,UAAU,KAAK;IACjC;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,wBAAwB,CAAC,MAAc,QAAkB,CAAC;YACxD;YACA;YACA,QAAQ,CAAC,OAAO,CAAC,IAAI;QACvB,CAAC;IAED;;GAEC,GACD,oBAAoB,CAAC;QACnB,MAAM,eAAoC,CAAC;QAE3C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,YAAY,CAAC,IAAI,GAAG;YACtB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,cAAc,SAAS,kBAAkB,CAAC;QAChD,MAAM,eAAe,IAAI;QAEzB,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,aAAa,MAAM,CAAC,KAAK,OAAO;YACrD,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;QAEA,OAAO,aAAa,QAAQ;IAC9B;AACF;AAuBO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,qBAAqB,CAAC,OAAe;QACnC,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC3B;IAEA;;GAEC,GACD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,CAAC,OAAO,CAAC,IAAI;IACtB;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,MAAM,QAAQ,gBAAgB,eAAe,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI;QACvE,MAAM,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,KAAK,GAAG,GAAG,KAAK,KAAK;QAEvD,OAAO;YACL;YACA;YACA,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,IAAI,GAAG,KAAK,UAAU;YACpC,SAAS,KAAK,IAAI,GAAG;QACvB;IACF;AACF;uCAMe"}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/api/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth API Module\n// Authentication API calls\n\nimport axios, { AxiosInstance } from 'axios';\nimport { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';\nimport { apiUtils } from '@/shared/utils/api';\nimport {\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  RefreshTokenRequest,\n  RefreshTokenResponse,\n  SystemUser,\n  RegisteredUser,\n} from '../types';\n\n// ============================================================================\n// AUTH API ENDPOINTS\n// ============================================================================\n\nconst AUTH_ENDPOINTS = {\n  // SystemUser authentication endpoints\n  login: '/system-auth/login',\n  profile: '/system-auth/profile',\n  refresh: '/system-auth/refresh',\n  logout: '/system-auth/logout',\n  logoutAll: '/system-auth/logout-all',\n  createUser: '/system-auth/create-user',\n  changePassword: '/system-auth/change-password',\n\n  // RegisteredUser endpoints (for future User Manager module)\n  userRegister: '/users/register',\n  userLogin: '/users/login',\n  verifyEmail: '/users/verify-email',\n} as const;\n\n// ============================================================================\n// AUTH API CLIENT\n// ============================================================================\n\nclass AuthApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_CONFIG.BASE_URL,\n      timeout: API_CONFIG.TIMEOUT,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - add JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(apiUtils.handleError(error))\n    );\n\n    // Response interceptor - handle errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.clearTokens();\n            // Redirect to login will be handled by auth store\n            throw apiUtils.handleError(refreshError);\n          }\n        }\n\n        throw apiUtils.handleError(error);\n      }\n    );\n  }\n\n  // ========================================================================\n  // TOKEN MANAGEMENT\n  // ========================================================================\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);\n    }\n  }\n\n  private getRefreshToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n    return null;\n  }\n\n  private setRefreshToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);\n    }\n  }\n\n  private clearTokens(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n  }\n\n  // ========================================================================\n  // AUTH API METHODS\n  // ========================================================================\n\n  /**\n   * Login user (both SystemUser and RegisteredUser)\n   */\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await this.instance.post<LoginResponse>(\n        AUTH_ENDPOINTS.login,\n        credentials\n      );\n\n      const { accessToken, refreshToken } = response.data;\n      this.setToken(accessToken);\n      this.setRefreshToken(refreshToken);\n\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Get current user profile\n   */\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    try {\n      const response = await this.instance.get<SystemUser | RegisteredUser>(\n        AUTH_ENDPOINTS.profile\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Refresh access token\n   */\n  async refreshToken(): Promise<RefreshTokenResponse> {\n    try {\n      const refreshToken = this.getRefreshToken();\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await this.instance.post<RefreshTokenResponse>(\n        AUTH_ENDPOINTS.refresh,\n        { refresh_token: refreshToken }\n      );\n\n      const { accessToken, refreshToken: newRefreshToken } = response.data;\n      this.setToken(accessToken);\n      this.setRefreshToken(newRefreshToken);\n\n      return response.data;\n    } catch (error) {\n      this.clearTokens();\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logout);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Create new system user (admin only) - sử dụng system-auth endpoint\n   */\n  async createSystemUser(userData: RegisterRequest & { role: string }): Promise<SystemUser> {\n    try {\n      const response = await this.instance.post<SystemUser>(\n        AUTH_ENDPOINTS.createUser,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Change password for current user\n   */\n  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.changePassword, data);\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout from all devices\n   */\n  async logoutAllDevices(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logoutAll);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout all devices API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Register new registered user\n   */\n  async registerUser(userData: RegisterRequest): Promise<RegisteredUser> {\n    try {\n      const response = await this.instance.post<RegisteredUser>(\n        AUTH_ENDPOINTS.userRegister,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Verify email for registered user\n   */\n  async verifyEmail(token: string): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.verifyEmail, { token });\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  /**\n   * Get current access token\n   */\n  getCurrentToken(): string | null {\n    return this.getToken();\n  }\n}\n\n// ============================================================================\n// EXPORT SINGLETON INSTANCE\n// ============================================================================\n\nexport const authApi = new AuthApiClient();\nexport default authApi;\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,2BAA2B;;;;;AAG3B;AACA;AAFA;;;;AAaA,+EAA+E;AAC/E,qBAAqB;AACrB,+EAA+E;AAE/E,MAAM,iBAAiB;IACrB,sCAAsC;IACtC,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,WAAW;IACX,YAAY;IACZ,gBAAgB;IAEhB,4DAA4D;IAC5D,cAAc;IACd,WAAW;IACX,aAAa;AACf;AAEA,+EAA+E;AAC/E,kBAAkB;AAClB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS,mIAAA,CAAA,aAAU,CAAC,QAAQ;YAC5B,SAAS,mIAAA,CAAA,aAAU,CAAC,OAAO;YAC3B,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAGjD,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,WAAW;oBAChB,kDAAkD;oBAClD,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;gBAC7B;YACF;YAEA,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IAEJ;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAEnE,WAA0B;QAChC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,SAAS,KAAa,EAAQ;QACpC,uCAAmC;;QAEnC;IACF;IAEQ,kBAAiC;QACvC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,gBAAgB,KAAa,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEQ,cAAoB;QAC1B,uCAAmC;;QAGnC;IACF;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E;;GAEC,GACD,MAAM,MAAM,WAAyB,EAA0B;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,KAAK,EACpB;YAGF,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,SAAS,IAAI;YACnD,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,aAAmD;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,eAAe,OAAO;YAExB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAA8C;QAClD,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,OAAO,EACtB;gBAAE,eAAe;YAAa;YAGhC,MAAM,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE,GAAG,SAAS,IAAI;YACpE,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,SAAwB;QAC5B,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,MAAM;QAChD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,2BAA2B;QAC1C,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAA4C,EAAuB;QACxF,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,UAAU,EACzB;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,IAAsD,EAAiB;QAC1F,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,cAAc,EAAE;QAC1D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,uCAAuC;QACtD,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAyB,EAA2B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,YAAY,EAC3B;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAiB;QAC9C,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,WAAW,EAAE;gBAAE;YAAM;QAC/D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,kBAAiC;QAC/B,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF;AAMO,MAAM,UAAU,IAAI;uCACZ"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/types/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth Module Types\n// Authentication related types\n\nimport { BaseEntity } from '@/shared/types/common';\n\n// ============================================================================\n// USER TYPES\n// ============================================================================\n\nexport interface SystemUser extends BaseEntity {\n  username: string;\n  email: string;\n  role: 'admin' | 'editor' | 'moderator';\n  isActive: boolean;\n  lastLoginAt: Date;\n}\n\nexport interface RegisteredUser extends BaseEntity {\n  username: string;\n  email: string;\n  tier: 'free' | 'premium' | 'enterprise';\n  isActive: boolean;\n  isEmailVerified: boolean;\n  apiCallsUsed: number;\n  apiCallsLimit: number | null;\n  subscriptionEndDate: Date | null;\n  lastLoginAt: Date;\n}\n\nexport type User = SystemUser | RegisteredUser;\n\n// ============================================================================\n// ROLE & TIER TYPES\n// ============================================================================\n\nexport type SystemRole = 'admin' | 'editor' | 'moderator';\nexport type RegisteredUserTier = 'free' | 'premium' | 'enterprise';\n\n// ============================================================================\n// JWT TOKEN TYPES\n// ============================================================================\n\nexport interface SystemUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  role: SystemRole;\n  userType: 'system';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface RegisteredUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  tier: RegisteredUserTier;\n  userType: 'registered';\n  isEmailVerified: boolean;\n  iat?: number;\n  exp?: number;\n}\n\nexport type UserToken = SystemUserToken | RegisteredUserToken;\n\n// ============================================================================\n// AUTH REQUEST/RESPONSE TYPES\n// ============================================================================\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  accessToken: string;\n  refreshToken: string;\n  user?: SystemUser | RegisteredUser; // User có thể không có trong response\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport interface RefreshTokenRequest {\n  refresh_token: string;\n}\n\nexport interface RefreshTokenResponse {\n  accessToken: string;\n  refreshToken: string;\n}\n\n// ============================================================================\n// AUTH STATE TYPES\n// ============================================================================\n\nexport interface AuthState {\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nexport interface AuthActions {\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  refreshToken: () => Promise<void>;\n  clearError: () => void;\n}\n\n// ============================================================================\n// PERMISSION TYPES\n// ============================================================================\n\nexport interface PermissionState {\n  // User type checks\n  isSystemUser: boolean;\n  isRegisteredUser: boolean;\n\n  // Role checks (SystemUser)\n  isAdmin: boolean;\n  isEditor: boolean;\n  isModerator: boolean;\n\n  // Tier checks (RegisteredUser)\n  isFree: boolean;\n  isPremium: boolean;\n  isEnterprise: boolean;\n\n  // Permission checks\n  canManageUsers: boolean;\n  canManageLeagues: boolean;\n  canManageTeams: boolean;\n  canManageFixtures: boolean;\n  canSync: boolean;\n  canViewAnalytics: boolean;\n\n  // Current user\n  currentUser: SystemUser | RegisteredUser | null;\n}\n\n// ============================================================================\n// AUTH HOOK TYPES\n// ============================================================================\n\nexport interface UseAuthReturn extends AuthState, AuthActions { }\n\nexport interface UsePermissionsReturn extends PermissionState { }\n\n// ============================================================================\n// TYPE GUARDS\n// ============================================================================\n\nexport const isSystemUser = (user: SystemUser | RegisteredUser | null): user is SystemUser => {\n  return user !== null && 'role' in user;\n};\n\nexport const isRegisteredUser = (user: SystemUser | RegisteredUser | null): user is RegisteredUser => {\n  return user !== null && 'tier' in user;\n};\n\nexport const isSystemUserToken = (token: UserToken): token is SystemUserToken => {\n  return token.userType === 'system';\n};\n\nexport const isRegisteredUserToken = (token: UserToken): token is RegisteredUserToken => {\n  return token.userType === 'registered';\n};\n\n// ============================================================================\n// EXPORT ALL\n// ============================================================================\n\n// Remove circular export\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,+BAA+B;;;;;;;AA4JxB,MAAM,eAAe,CAAC;IAC3B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,QAAQ,KAAK;AAC5B;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,MAAM,QAAQ,KAAK;AAC5B,GAEA,+EAA+E;CAC/E,aAAa;CACb,+EAA+E;CAE/E,yBAAyB"}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho SystemUser authentication (FECMS chỉ sử dụng SystemUser)\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi } from '@/modules/auth/api';\nimport {\n  SystemUser,\n  RegisteredUser,\n  LoginRequest,\n  AuthState,\n  AuthActions,\n  UseAuthReturn,\n  UsePermissionsReturn,\n  isSystemUser,\n  isRegisteredUser,\n} from '@/modules/auth/types';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthStoreState extends AuthState, AuthActions {\n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthStoreState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await authApi.login(credentials);\n\n          // Nếu response có user, sử dụng nó. Nếu không, gọi getProfile\n          let user = response.user;\n          if (!user) {\n            user = await authApi.getProfile();\n          }\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Đăng nhập thất bại';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTIONS\n      // ========================================================================\n      logout: () => {\n        try {\n          authApi.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      logoutAllDevices: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.logoutAllDevices();\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to logout from all devices';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // SYSTEM USER MANAGEMENT ACTIONS\n      // ========================================================================\n      createSystemUser: async (userData: any) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const newUser = await authApi.createSystemUser(userData);\n          set({ isLoading: false, error: null });\n          return newUser;\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to create system user';\n\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      changePassword: async (data: { currentPassword: string; newPassword: string }) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.changePassword(data);\n          set({ isLoading: false, error: null });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to change password';\n\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const user = await authApi.getProfile();\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Không thể lấy thông tin người dùng';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return isSystemUser(user);\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return isRegisteredUser(user);\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n\n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n\n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n\n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n\n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n\n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n\n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    logoutAllDevices,\n    getProfile,\n    createSystemUser,\n    changePassword,\n    clearError\n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    logoutAllDevices,\n    getProfile,\n    createSystemUser,\n    changePassword,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,6EAA6E;;;;;;AAI7E;AACA;AAHA;AACA;;;;;AA8BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,sIAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAErC,8DAA8D;gBAC9D,IAAI,OAAO,SAAS,IAAI;gBACxB,IAAI,CAAC,MAAM;oBACT,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBACjC;gBAEA,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,sIAAA,CAAA,UAAO,CAAC,MAAM;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,kBAAkB;YAChB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,sIAAA,CAAA,UAAO,CAAC,gBAAgB;gBAC9B,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,iCAAiC;QACjC,2EAA2E;QAC3E,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,UAAU,MAAM,sIAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;gBAC/C,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAK;gBACpC,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,sIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAC7B,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAK;YACtC,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBAErC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;QACtB;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,cAAc,EACd,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Login Page\n// Dual authentication system theo CMS Development Guide\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Typography,\n  Alert,\n  Space,\n  Divider,\n  Row,\n  Col\n} from 'antd';\nimport {\n  UserOutlined,\n  LockOutlined,\n  LoginOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\nimport { useAuth } from '@/stores/auth-store';\nimport { LoginRequest } from '@/types';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// LOGIN PAGE COMPONENT\n// ============================================================================\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();\n  const [form] = Form.useForm();\n\n  // Redirect nếu đã đăng nhập\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  // Clear error khi component unmount\n  useEffect(() => {\n    return () => {\n      clearError();\n    };\n  }, [clearError]);\n\n  // ========================================================================\n  // FORM SUBMIT HANDLER\n  // ========================================================================\n  const handleSubmit = async (values: LoginRequest) => {\n    try {\n      await login(values);\n      // Redirect sẽ được xử lý bởi useEffect\n    } catch (error) {\n      // Error đã được xử lý trong store\n      console.error('Login failed:', error);\n    }\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\">\n      <Row justify=\"center\" align=\"middle\" className=\"w-full\">\n        <Col xs={24} sm={20} md={16} lg={12} xl={8}>\n          <Card\n            className=\"shadow-2xl border-0\"\n            bodyStyle={{ padding: '2rem' }}\n          >\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"bg-blue-500 p-3 rounded-full\">\n                  <ApiOutlined className=\"text-white text-2xl\" />\n                </div>\n              </div>\n              <Title level={2} className=\"mb-2\">\n                APISportsGame CMS\n              </Title>\n              <Text type=\"secondary\" className=\"text-base\">\n                Đăng nhập vào hệ thống quản lý\n              </Text>\n            </div>\n\n            {/* Error Alert */}\n            {error && (\n              <Alert\n                message=\"Đăng nhập thất bại\"\n                description={error}\n                type=\"error\"\n                showIcon\n                closable\n                onClose={clearError}\n                className=\"mb-6\"\n              />\n            )}\n\n            {/* Login Form */}\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleSubmit}\n              layout=\"vertical\"\n              size=\"large\"\n              autoComplete=\"off\"\n            >\n              <Form.Item\n                name=\"username\"\n                label=\"Tên đăng nhập\"\n                rules={[\n                  {\n                    required: true,\n                    message: 'Vui lòng nhập tên đăng nhập!'\n                  },\n                  {\n                    min: 3,\n                    message: 'Tên đăng nhập phải có ít nhất 3 ký tự!'\n                  }\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"Nhập tên đăng nhập\"\n                  autoComplete=\"username\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                label=\"Mật khẩu\"\n                rules={[\n                  {\n                    required: true,\n                    message: 'Vui lòng nhập mật khẩu!'\n                  },\n                  {\n                    min: 6,\n                    message: 'Mật khẩu phải có ít nhất 6 ký tự!'\n                  }\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"Nhập mật khẩu\"\n                  autoComplete=\"current-password\"\n                />\n              </Form.Item>\n\n              <Form.Item className=\"mb-6\">\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={isLoading}\n                  icon={<LoginOutlined />}\n                  block\n                  className=\"h-12 text-base font-medium\"\n                >\n                  {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}\n                </Button>\n              </Form.Item>\n            </Form>\n\n            <Divider>\n              <Text type=\"secondary\">Thông tin hệ thống</Text>\n            </Divider>\n\n            {/* System Info */}\n            <div className=\"text-center space-y-2\">\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <Title level={5} className=\"mb-2 text-blue-700\">\n                  Frontend CMS Login\n                </Title>\n                <Space direction=\"vertical\" size=\"small\">\n                  <Text type=\"secondary\" className=\"text-sm\">\n                    🔐 SystemUser Authentication\n                  </Text>\n                  <Text type=\"secondary\" className=\"text-sm\">\n                    Roles: Admin, Editor, Moderator\n                  </Text>\n                  <Text type=\"warning\" className=\"text-xs\">\n                    Endpoint: /system-auth/login\n                  </Text>\n                </Space>\n              </div>\n\n              <Text type=\"secondary\" className=\"text-xs\">\n                API Proxy: http://localhost:4000/api\n              </Text>\n            </div>\n\n            {/* Footer */}\n            <div className=\"text-center mt-6 pt-4 border-t border-gray-100\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                APISportsGame CMS v1.0.0 - Phase 1 Implementation\n              </Text>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,iCAAiC;AACjC,wDAAwD;AAExD;AACA;AAmBA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;AAPA;;;;;;;AA4BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAMnB,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvE,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,2EAA2E;IAC3E,sBAAsB;IACtB,2EAA2E;IAC3E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,MAAM;QACZ,uCAAuC;QACzC,EAAE,OAAO,OAAO;YACd,kCAAkC;YAClC,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAC3E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,4KAAA,CAAA,MAAG;YAAC,SAAQ;YAAS,OAAM;YAAS,WAAU;sBAC7C,cAAA,8OAAC,4KAAA,CAAA,MAAG;gBAAC,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;0BACvC,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBACH,WAAU;oBACV,WAAW;wBAAE,SAAS;oBAAO;;sCAG7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG3B,8OAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAO;;;;;;8CAGlC,8OAAC;oCAAK,MAAK;oCAAY,WAAU;8CAAY;;;;;;;;;;;;wBAM9C,uBACC,8OAAC,gLAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;4BACR,QAAQ;4BACR,SAAS;4BACT,WAAU;;;;;;sCAKd,8OAAC,8KAAA,CAAA,OAAI;4BACH,MAAM;4BACN,MAAK;4BACL,UAAU;4BACV,QAAO;4BACP,MAAK;4BACL,cAAa;;8CAEb,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CACE,UAAU;4CACV,SAAS;wCACX;wCACA;4CACE,KAAK;4CACL,SAAS;wCACX;qCACD;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,cAAa;;;;;;;;;;;8CAIjB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CACE,UAAU;4CACV,SAAS;wCACX;wCACA;4CACE,KAAK;4CACL,SAAS;wCACX;qCACD;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,cAAa;;;;;;;;;;;8CAIjB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,WAAU;8CACnB,cAAA,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;wCACpB,KAAK;wCACL,WAAU;kDAET,YAAY,sBAAsB;;;;;;;;;;;;;;;;;sCAKzC,8OAAC,oLAAA,CAAA,UAAO;sCACN,cAAA,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAqB;;;;;;sDAGhD,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAW,MAAK;;8DAC/B,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;8DAG3C,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;8DAG3C,8OAAC;oDAAK,MAAK;oDAAU,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC;oCAAK,MAAK;oCAAY,WAAU;8CAAU;;;;;;;;;;;;sCAM7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD"}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}