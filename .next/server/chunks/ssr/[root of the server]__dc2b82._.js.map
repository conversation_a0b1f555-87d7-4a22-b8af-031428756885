{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/constants/index.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Constants\n// Application-wide constants\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nexport const API_CONFIG = {\n  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',\n  TIMEOUT: 10000,\n  RETRY_ATTEMPTS: 3,\n} as const;\n\n// ============================================================================\n// PAGINATION DEFAULTS\n// ============================================================================\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_PAGE_SIZE: 10,\n  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],\n  MAX_PAGE_SIZE: 100,\n} as const;\n\n// ============================================================================\n// VALIDATION RULES\n// ============================================================================\n\nexport const VALIDATION = {\n  USERNAME: {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: 50,\n    PATTERN: /^[a-zA-Z0-9_]+$/,\n  },\n  PASSWORD: {\n    MIN_LENGTH: 6,\n    MAX_LENGTH: 100,\n  },\n  EMAIL: {\n    PATTERN: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  },\n  SEARCH: {\n    MIN_LENGTH: 2,\n    MAX_LENGTH: 100,\n  },\n} as const;\n\n// ============================================================================\n// UI CONSTANTS\n// ============================================================================\n\nexport const UI = {\n  DEBOUNCE_DELAY: 300,\n  ANIMATION_DURATION: 200,\n  NOTIFICATION_DURATION: 4000,\n  MODAL_WIDTH: {\n    SMALL: 400,\n    MEDIUM: 600,\n    LARGE: 800,\n    EXTRA_LARGE: 1000,\n  },\n} as const;\n\n// ============================================================================\n// STATUS COLORS\n// ============================================================================\n\nexport const STATUS_COLORS = {\n  SUCCESS: '#52c41a',\n  ERROR: '#ff4d4f',\n  WARNING: '#faad14',\n  INFO: '#1890ff',\n  PROCESSING: '#722ed1',\n  DEFAULT: '#d9d9d9',\n} as const;\n\n// ============================================================================\n// USER ROLES & TIERS\n// ============================================================================\n\nexport const USER_ROLES = {\n  ADMIN: 'admin',\n  EDITOR: 'editor',\n  MODERATOR: 'moderator',\n} as const;\n\nexport const USER_TIERS = {\n  FREE: 'free',\n  PREMIUM: 'premium',\n  ENTERPRISE: 'enterprise',\n} as const;\n\nexport const ROLE_COLORS = {\n  [USER_ROLES.ADMIN]: 'red',\n  [USER_ROLES.EDITOR]: 'blue',\n  [USER_ROLES.MODERATOR]: 'green',\n} as const;\n\nexport const TIER_COLORS = {\n  [USER_TIERS.FREE]: 'default',\n  [USER_TIERS.PREMIUM]: 'gold',\n  [USER_TIERS.ENTERPRISE]: 'purple',\n} as const;\n\n// ============================================================================\n// API LIMITS\n// ============================================================================\n\nexport const API_LIMITS = {\n  FREE_TIER: 1000,\n  PREMIUM_TIER: 50000,\n  ENTERPRISE_TIER: 200000,\n  UNLIMITED: null,\n} as const;\n\n// ============================================================================\n// DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// ROUTES\n// ============================================================================\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/auth/login',\n  DASHBOARD: '/dashboard',\n  USERS: '/dashboard/users',\n  LEAGUES: '/dashboard/leagues',\n  TEAMS: '/dashboard/teams',\n  FIXTURES: '/dashboard/fixtures',\n  ANALYTICS: '/dashboard/analytics',\n  SYNC: '/dashboard/sync',\n  PROFILE: '/dashboard/profile',\n  SETTINGS: '/dashboard/settings',\n} as const;\n\n// ============================================================================\n// LOCAL STORAGE KEYS\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  ACCESS_TOKEN: 'access_token',\n  REFRESH_TOKEN: 'refresh_token',\n  USER_PREFERENCES: 'user_preferences',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n} as const;\n\n// ============================================================================\n// ERROR MESSAGES\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied. Insufficient permissions.',\n  NOT_FOUND: 'The requested resource was not found.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  SERVER_ERROR: 'Internal server error. Please try again later.',\n  UNKNOWN_ERROR: 'An unknown error occurred.',\n} as const;\n\n// ============================================================================\n// SUCCESS MESSAGES\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  CREATED: 'Created successfully',\n  UPDATED: 'Updated successfully',\n  DELETED: 'Deleted successfully',\n  SAVED: 'Saved successfully',\n  SYNCED: 'Synced successfully',\n  EXPORTED: 'Exported successfully',\n} as const;\n\n// ============================================================================\n// QUERY KEYS\n// ============================================================================\n\nexport const QUERY_KEYS = {\n  AUTH: ['auth'],\n  USERS: ['users'],\n  LEAGUES: ['leagues'],\n  TEAMS: ['teams'],\n  FIXTURES: ['fixtures'],\n  DASHBOARD: ['dashboard'],\n  SYNC: ['sync'],\n} as const;\n\n// ============================================================================\n// PERMISSIONS\n// ============================================================================\n\nexport const PERMISSIONS = {\n  USERS: {\n    VIEW: 'users:view',\n    CREATE: 'users:create',\n    UPDATE: 'users:update',\n    DELETE: 'users:delete',\n  },\n  LEAGUES: {\n    VIEW: 'leagues:view',\n    CREATE: 'leagues:create',\n    UPDATE: 'leagues:update',\n    DELETE: 'leagues:delete',\n  },\n  TEAMS: {\n    VIEW: 'teams:view',\n    CREATE: 'teams:create',\n    UPDATE: 'teams:update',\n    DELETE: 'teams:delete',\n  },\n  FIXTURES: {\n    VIEW: 'fixtures:view',\n    CREATE: 'fixtures:create',\n    UPDATE: 'fixtures:update',\n    DELETE: 'fixtures:delete',\n  },\n  SYNC: {\n    VIEW: 'sync:view',\n    EXECUTE: 'sync:execute',\n  },\n  ANALYTICS: {\n    VIEW: 'analytics:view',\n  },\n} as const;\n\n// ============================================================================\n// EXPORT TYPES\n// ============================================================================\n\nexport type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];\nexport type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS];\nexport type Route = typeof ROUTES[keyof typeof ROUTES];\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS][keyof typeof PERMISSIONS[keyof typeof PERMISSIONS]];\n\n// ============================================================================\n// DEFAULT EXPORT\n// ============================================================================\n\nexport default {\n  API_CONFIG,\n  PAGINATION,\n  VALIDATION,\n  UI,\n  STATUS_COLORS,\n  USER_ROLES,\n  USER_TIERS,\n  ROLE_COLORS,\n  TIER_COLORS,\n  API_LIMITS,\n  DATE_FORMATS,\n  ROUTES,\n  STORAGE_KEYS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  QUERY_KEYS,\n  PERMISSIONS,\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,6BAA6B;AAE7B,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;;;;;;;;;;;;;;;;;;;;;AAExE,MAAM,aAAa;IACxB,UAAU,iEAAmC;IAC7C,SAAS;IACT,gBAAgB;AAClB;AAMO,MAAM,aAAa;IACxB,cAAc;IACd,mBAAmB;IACnB,mBAAmB;QAAC;QAAI;QAAI;QAAI;KAAI;IACpC,eAAe;AACjB;AAMO,MAAM,aAAa;IACxB,UAAU;QACR,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IACA,UAAU;QACR,YAAY;QACZ,YAAY;IACd;IACA,OAAO;QACL,SAAS;IACX;IACA,QAAQ;QACN,YAAY;QACZ,YAAY;IACd;AACF;AAMO,MAAM,KAAK;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,aAAa;QACX,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;AACF;AAMO,MAAM,gBAAgB;IAC3B,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,YAAY;IACZ,SAAS;AACX;AAMO,MAAM,aAAa;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,YAAY;AACd;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,KAAK,CAAC,EAAE;IACpB,CAAC,WAAW,MAAM,CAAC,EAAE;IACrB,CAAC,WAAW,SAAS,CAAC,EAAE;AAC1B;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,IAAI,CAAC,EAAE;IACnB,CAAC,WAAW,OAAO,CAAC,EAAE;IACtB,CAAC,WAAW,UAAU,CAAC,EAAE;AAC3B;AAMO,MAAM,aAAa;IACxB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,WAAW;AACb;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;AAMO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,WAAW;IACX,OAAO;IACP,SAAS;IACT,OAAO;IACP,UAAU;IACV,WAAW;IACX,MAAM;IACN,SAAS;IACT,UAAU;AACZ;AAMO,MAAM,eAAe;IAC1B,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,OAAO;IACP,UAAU;AACZ;AAMO,MAAM,iBAAiB;IAC5B,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,eAAe;AACjB;AAMO,MAAM,mBAAmB;IAC9B,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,UAAU;AACZ;AAMO,MAAM,aAAa;IACxB,MAAM;QAAC;KAAO;IACd,OAAO;QAAC;KAAQ;IAChB,SAAS;QAAC;KAAU;IACpB,OAAO;QAAC;KAAQ;IAChB,UAAU;QAAC;KAAW;IACtB,WAAW;QAAC;KAAY;IACxB,MAAM;QAAC;KAAO;AAChB;AAMO,MAAM,cAAc;IACzB,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,UAAU;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF;uCAee;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/api.ts"], "sourcesContent": ["// APISportsGame CMS - Shared API Utilities\n// Common API utilities và error handling\n\nimport { AxiosError } from 'axios';\n\n// ============================================================================\n// API ERROR HANDLING\n// ============================================================================\n\nexport interface ApiErrorResponse {\n  statusCode: number;\n  message: string | string[];\n  error: string;\n  timestamp: string;\n  path: string;\n}\n\nexport class ApiError extends Error {\n  public statusCode: number;\n  public response?: ApiErrorResponse;\n\n  constructor(error: AxiosError) {\n    const response = error.response?.data as ApiErrorResponse;\n    const message = Array.isArray(response?.message) \n      ? response.message.join(', ')\n      : response?.message || error.message || 'An error occurred';\n\n    super(message);\n    this.name = 'ApiError';\n    this.statusCode = error.response?.status || 500;\n    this.response = response;\n  }\n}\n\n// ============================================================================\n// API RESPONSE UTILITIES\n// ============================================================================\n\nexport const apiUtils = {\n  /**\n   * Handle API errors consistently\n   */\n  handleError: (error: unknown): ApiError => {\n    if (error instanceof AxiosError) {\n      return new ApiError(error);\n    }\n    \n    if (error instanceof Error) {\n      const apiError = new ApiError({} as AxiosError);\n      apiError.message = error.message;\n      return apiError;\n    }\n\n    const apiError = new ApiError({} as AxiosError);\n    apiError.message = 'Unknown error occurred';\n    return apiError;\n  },\n\n  /**\n   * Extract error message for display\n   */\n  getErrorMessage: (error: unknown): string => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.message;\n  },\n\n  /**\n   * Check if error is specific status code\n   */\n  isErrorStatus: (error: unknown, statusCode: number): boolean => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.statusCode === statusCode;\n  },\n\n  /**\n   * Check if error is unauthorized\n   */\n  isUnauthorized: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 401);\n  },\n\n  /**\n   * Check if error is forbidden\n   */\n  isForbidden: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 403);\n  },\n\n  /**\n   * Check if error is not found\n   */\n  isNotFound: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 404);\n  },\n\n  /**\n   * Check if error is validation error\n   */\n  isValidationError: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 400);\n  },\n\n  /**\n   * Format pagination params\n   */\n  formatPaginationParams: (page: number, limit: number) => ({\n    page,\n    limit,\n    offset: (page - 1) * limit,\n  }),\n\n  /**\n   * Format filter params (remove undefined values)\n   */\n  formatFilterParams: (filters: Record<string, any>) => {\n    const cleanFilters: Record<string, any> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        cleanFilters[key] = value;\n      }\n    });\n\n    return cleanFilters;\n  },\n\n  /**\n   * Build query string from params\n   */\n  buildQueryString: (params: Record<string, any>): string => {\n    const cleanParams = apiUtils.formatFilterParams(params);\n    const searchParams = new URLSearchParams();\n\n    Object.entries(cleanParams).forEach(([key, value]) => {\n      if (Array.isArray(value)) {\n        value.forEach(v => searchParams.append(key, String(v)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    });\n\n    return searchParams.toString();\n  },\n};\n\n// ============================================================================\n// PAGINATION UTILITIES\n// ============================================================================\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface PaginationMeta {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: PaginationMeta;\n}\n\nexport const paginationUtils = {\n  /**\n   * Calculate total pages\n   */\n  calculateTotalPages: (total: number, limit: number): number => {\n    return Math.ceil(total / limit);\n  },\n\n  /**\n   * Calculate offset from page and limit\n   */\n  calculateOffset: (page: number, limit: number): number => {\n    return (page - 1) * limit;\n  },\n\n  /**\n   * Get pagination info for display\n   */\n  getPaginationInfo: (meta: PaginationMeta) => {\n    const start = paginationUtils.calculateOffset(meta.page, meta.limit) + 1;\n    const end = Math.min(start + meta.limit - 1, meta.total);\n    \n    return {\n      start,\n      end,\n      total: meta.total,\n      hasNext: meta.page < meta.totalPages,\n      hasPrev: meta.page > 1,\n    };\n  },\n};\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport default apiUtils;\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,yCAAyC;;;;;;;AAEzC;;AAcO,MAAM,iBAAiB;IACrB,WAAmB;IACnB,SAA4B;IAEnC,YAAY,KAAiB,CAAE;QAC7B,MAAM,WAAW,MAAM,QAAQ,EAAE;QACjC,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,WACpC,SAAS,OAAO,CAAC,IAAI,CAAC,QACtB,UAAU,WAAW,MAAM,OAAO,IAAI;QAE1C,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG,MAAM,QAAQ,EAAE,UAAU;QAC5C,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAMO,MAAM,WAAW;IACtB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,iBAAiB,8IAAA,CAAA,aAAU,EAAE;YAC/B,OAAO,IAAI,SAAS;QACtB;QAEA,IAAI,iBAAiB,OAAO;YAC1B,MAAM,WAAW,IAAI,SAAS,CAAC;YAC/B,SAAS,OAAO,GAAG,MAAM,OAAO;YAChC,OAAO;QACT;QAEA,MAAM,WAAW,IAAI,SAAS,CAAC;QAC/B,SAAS,OAAO,GAAG;QACnB,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,OAAO;IACzB;IAEA;;GAEC,GACD,eAAe,CAAC,OAAgB;QAC9B,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,UAAU,KAAK;IACjC;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,wBAAwB,CAAC,MAAc,QAAkB,CAAC;YACxD;YACA;YACA,QAAQ,CAAC,OAAO,CAAC,IAAI;QACvB,CAAC;IAED;;GAEC,GACD,oBAAoB,CAAC;QACnB,MAAM,eAAoC,CAAC;QAE3C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,YAAY,CAAC,IAAI,GAAG;YACtB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,cAAc,SAAS,kBAAkB,CAAC;QAChD,MAAM,eAAe,IAAI;QAEzB,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,aAAa,MAAM,CAAC,KAAK,OAAO;YACrD,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;QAEA,OAAO,aAAa,QAAQ;IAC9B;AACF;AAuBO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,qBAAqB,CAAC,OAAe;QACnC,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC3B;IAEA;;GAEC,GACD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,CAAC,OAAO,CAAC,IAAI;IACtB;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,MAAM,QAAQ,gBAAgB,eAAe,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI;QACvE,MAAM,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,KAAK,GAAG,GAAG,KAAK,KAAK;QAEvD,OAAO;YACL;YACA;YACA,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,IAAI,GAAG,KAAK,UAAU;YACpC,SAAS,KAAK,IAAI,GAAG;QACvB;IACF;AACF;uCAMe"}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/api/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth API Module\n// Authentication API calls\n\nimport axios, { AxiosInstance } from 'axios';\nimport { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';\nimport { apiUtils } from '@/shared/utils/api';\nimport {\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  RefreshTokenRequest,\n  RefreshTokenResponse,\n  SystemUser,\n  RegisteredUser,\n} from '../types';\n\n// ============================================================================\n// AUTH API ENDPOINTS\n// ============================================================================\n\nconst AUTH_ENDPOINTS = {\n  // SystemUser authentication endpoints\n  login: '/system-auth/login',\n  profile: '/system-auth/profile',\n  refresh: '/system-auth/refresh',\n  logout: '/system-auth/logout',\n  logoutAll: '/system-auth/logout-all',\n  createUser: '/system-auth/create-user',\n  changePassword: '/system-auth/change-password',\n\n  // RegisteredUser endpoints (for future User Manager module)\n  userRegister: '/users/register',\n  userLogin: '/users/login',\n  verifyEmail: '/users/verify-email',\n} as const;\n\n// ============================================================================\n// AUTH API CLIENT\n// ============================================================================\n\nclass AuthApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_CONFIG.BASE_URL,\n      timeout: API_CONFIG.TIMEOUT,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - add JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(apiUtils.handleError(error))\n    );\n\n    // Response interceptor - handle errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.clearTokens();\n            // Redirect to login will be handled by auth store\n            throw apiUtils.handleError(refreshError);\n          }\n        }\n\n        throw apiUtils.handleError(error);\n      }\n    );\n  }\n\n  // ========================================================================\n  // TOKEN MANAGEMENT\n  // ========================================================================\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);\n    }\n  }\n\n  private getRefreshToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n    return null;\n  }\n\n  private setRefreshToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);\n    }\n  }\n\n  private clearTokens(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n  }\n\n  // ========================================================================\n  // AUTH API METHODS\n  // ========================================================================\n\n  /**\n   * Login user (both SystemUser and RegisteredUser)\n   */\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await this.instance.post<LoginResponse>(\n        AUTH_ENDPOINTS.login,\n        credentials\n      );\n\n      const { accessToken, refreshToken } = response.data;\n      this.setToken(accessToken);\n      this.setRefreshToken(refreshToken);\n\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Get current user profile\n   */\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    try {\n      const response = await this.instance.get<SystemUser | RegisteredUser>(\n        AUTH_ENDPOINTS.profile\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Refresh access token\n   */\n  async refreshToken(): Promise<RefreshTokenResponse> {\n    try {\n      const refreshToken = this.getRefreshToken();\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await this.instance.post<RefreshTokenResponse>(\n        AUTH_ENDPOINTS.refresh,\n        { refresh_token: refreshToken }\n      );\n\n      const { accessToken, refreshToken: newRefreshToken } = response.data;\n      this.setToken(accessToken);\n      this.setRefreshToken(newRefreshToken);\n\n      return response.data;\n    } catch (error) {\n      this.clearTokens();\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logout);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Create new system user (admin only) - sử dụng system-auth endpoint\n   */\n  async createSystemUser(userData: RegisterRequest & { role: string }): Promise<SystemUser> {\n    try {\n      const response = await this.instance.post<SystemUser>(\n        AUTH_ENDPOINTS.createUser,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Change password for current user\n   */\n  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.changePassword, data);\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout from all devices\n   */\n  async logoutAllDevices(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logoutAll);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout all devices API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Register new registered user\n   */\n  async registerUser(userData: RegisterRequest): Promise<RegisteredUser> {\n    try {\n      const response = await this.instance.post<RegisteredUser>(\n        AUTH_ENDPOINTS.userRegister,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Verify email for registered user\n   */\n  async verifyEmail(token: string): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.verifyEmail, { token });\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  /**\n   * Get current access token\n   */\n  getCurrentToken(): string | null {\n    return this.getToken();\n  }\n}\n\n// ============================================================================\n// EXPORT SINGLETON INSTANCE\n// ============================================================================\n\nexport const authApi = new AuthApiClient();\nexport default authApi;\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,2BAA2B;;;;;AAG3B;AACA;AAFA;;;;AAaA,+EAA+E;AAC/E,qBAAqB;AACrB,+EAA+E;AAE/E,MAAM,iBAAiB;IACrB,sCAAsC;IACtC,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,WAAW;IACX,YAAY;IACZ,gBAAgB;IAEhB,4DAA4D;IAC5D,cAAc;IACd,WAAW;IACX,aAAa;AACf;AAEA,+EAA+E;AAC/E,kBAAkB;AAClB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS,mIAAA,CAAA,aAAU,CAAC,QAAQ;YAC5B,SAAS,mIAAA,CAAA,aAAU,CAAC,OAAO;YAC3B,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAGjD,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,WAAW;oBAChB,kDAAkD;oBAClD,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;gBAC7B;YACF;YAEA,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IAEJ;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAEnE,WAA0B;QAChC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,SAAS,KAAa,EAAQ;QACpC,uCAAmC;;QAEnC;IACF;IAEQ,kBAAiC;QACvC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,gBAAgB,KAAa,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEQ,cAAoB;QAC1B,uCAAmC;;QAGnC;IACF;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E;;GAEC,GACD,MAAM,MAAM,WAAyB,EAA0B;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,KAAK,EACpB;YAGF,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,SAAS,IAAI;YACnD,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,aAAmD;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,eAAe,OAAO;YAExB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAA8C;QAClD,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,OAAO,EACtB;gBAAE,eAAe;YAAa;YAGhC,MAAM,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE,GAAG,SAAS,IAAI;YACpE,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,SAAwB;QAC5B,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,MAAM;QAChD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,2BAA2B;QAC1C,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAA4C,EAAuB;QACxF,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,UAAU,EACzB;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,IAAsD,EAAiB;QAC1F,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,cAAc,EAAE;QAC1D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,uCAAuC;QACtD,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAyB,EAA2B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,YAAY,EAC3B;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAiB;QAC9C,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,WAAW,EAAE;gBAAE;YAAM;QAC/D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,kBAAiC;QAC/B,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF;AAMO,MAAM,UAAU,IAAI;uCACZ"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/types/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth Module Types\n// Authentication related types\n\nimport { BaseEntity } from '@/shared/types/common';\n\n// ============================================================================\n// USER TYPES\n// ============================================================================\n\nexport interface SystemUser extends BaseEntity {\n  username: string;\n  email: string;\n  role: 'admin' | 'editor' | 'moderator';\n  isActive: boolean;\n  lastLoginAt: Date;\n}\n\nexport interface RegisteredUser extends BaseEntity {\n  username: string;\n  email: string;\n  tier: 'free' | 'premium' | 'enterprise';\n  isActive: boolean;\n  isEmailVerified: boolean;\n  apiCallsUsed: number;\n  apiCallsLimit: number | null;\n  subscriptionEndDate: Date | null;\n  lastLoginAt: Date;\n}\n\nexport type User = SystemUser | RegisteredUser;\n\n// ============================================================================\n// ROLE & TIER TYPES\n// ============================================================================\n\nexport type SystemRole = 'admin' | 'editor' | 'moderator';\nexport type RegisteredUserTier = 'free' | 'premium' | 'enterprise';\n\n// ============================================================================\n// JWT TOKEN TYPES\n// ============================================================================\n\nexport interface SystemUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  role: SystemRole;\n  userType: 'system';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface RegisteredUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  tier: RegisteredUserTier;\n  userType: 'registered';\n  isEmailVerified: boolean;\n  iat?: number;\n  exp?: number;\n}\n\nexport type UserToken = SystemUserToken | RegisteredUserToken;\n\n// ============================================================================\n// AUTH REQUEST/RESPONSE TYPES\n// ============================================================================\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  accessToken: string;\n  refreshToken: string;\n  user?: SystemUser | RegisteredUser; // User có thể không có trong response\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport interface RefreshTokenRequest {\n  refresh_token: string;\n}\n\nexport interface RefreshTokenResponse {\n  accessToken: string;\n  refreshToken: string;\n}\n\n// ============================================================================\n// AUTH STATE TYPES\n// ============================================================================\n\nexport interface AuthState {\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nexport interface AuthActions {\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  refreshToken: () => Promise<void>;\n  clearError: () => void;\n}\n\n// ============================================================================\n// PERMISSION TYPES\n// ============================================================================\n\nexport interface PermissionState {\n  // User type checks\n  isSystemUser: boolean;\n  isRegisteredUser: boolean;\n\n  // Role checks (SystemUser)\n  isAdmin: boolean;\n  isEditor: boolean;\n  isModerator: boolean;\n\n  // Tier checks (RegisteredUser)\n  isFree: boolean;\n  isPremium: boolean;\n  isEnterprise: boolean;\n\n  // Permission checks\n  canManageUsers: boolean;\n  canManageLeagues: boolean;\n  canManageTeams: boolean;\n  canManageFixtures: boolean;\n  canSync: boolean;\n  canViewAnalytics: boolean;\n\n  // Current user\n  currentUser: SystemUser | RegisteredUser | null;\n}\n\n// ============================================================================\n// AUTH HOOK TYPES\n// ============================================================================\n\nexport interface UseAuthReturn extends AuthState, AuthActions { }\n\nexport interface UsePermissionsReturn extends PermissionState { }\n\n// ============================================================================\n// TYPE GUARDS\n// ============================================================================\n\nexport const isSystemUser = (user: SystemUser | RegisteredUser | null): user is SystemUser => {\n  return user !== null && 'role' in user;\n};\n\nexport const isRegisteredUser = (user: SystemUser | RegisteredUser | null): user is RegisteredUser => {\n  return user !== null && 'tier' in user;\n};\n\nexport const isSystemUserToken = (token: UserToken): token is SystemUserToken => {\n  return token.userType === 'system';\n};\n\nexport const isRegisteredUserToken = (token: UserToken): token is RegisteredUserToken => {\n  return token.userType === 'registered';\n};\n\n// ============================================================================\n// EXPORT ALL\n// ============================================================================\n\n// Remove circular export\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,+BAA+B;;;;;;;AA4JxB,MAAM,eAAe,CAAC;IAC3B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,QAAQ,KAAK;AAC5B;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,MAAM,QAAQ,KAAK;AAC5B,GAEA,+EAA+E;CAC/E,aAAa;CACb,+EAA+E;CAE/E,yBAAyB"}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho SystemUser authentication (FECMS chỉ sử dụng SystemUser)\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi } from '@/modules/auth/api';\nimport {\n  SystemUser,\n  RegisteredUser,\n  LoginRequest,\n  AuthState,\n  AuthActions,\n  UseAuthReturn,\n  UsePermissionsReturn,\n  isSystemUser,\n  isRegisteredUser,\n} from '@/modules/auth/types';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthStoreState extends AuthState, AuthActions {\n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthStoreState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await authApi.login(credentials);\n\n          // Nếu response có user, sử dụng nó. Nếu không, gọi getProfile\n          let user = response.user;\n          if (!user) {\n            user = await authApi.getProfile();\n          }\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Đăng nhập thất bại';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTIONS\n      // ========================================================================\n      logout: () => {\n        try {\n          authApi.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      logoutAllDevices: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.logoutAllDevices();\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to logout from all devices';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // SYSTEM USER MANAGEMENT ACTIONS\n      // ========================================================================\n      createSystemUser: async (userData: any) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const newUser = await authApi.createSystemUser(userData);\n          set({ isLoading: false, error: null });\n          return newUser;\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to create system user';\n\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      changePassword: async (data: { currentPassword: string; newPassword: string }) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.changePassword(data);\n          set({ isLoading: false, error: null });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Failed to change password';\n\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const user = await authApi.getProfile();\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Không thể lấy thông tin người dùng';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return isSystemUser(user);\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return isRegisteredUser(user);\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n\n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n\n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n\n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n\n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n\n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n\n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    logoutAllDevices,\n    getProfile,\n    createSystemUser,\n    changePassword,\n    clearError\n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    logoutAllDevices,\n    getProfile,\n    createSystemUser,\n    changePassword,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,6EAA6E;;;;;;AAI7E;AACA;AAHA;AACA;;;;;AA8BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,sIAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAErC,8DAA8D;gBAC9D,IAAI,OAAO,SAAS,IAAI;gBACxB,IAAI,CAAC,MAAM;oBACT,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBACjC;gBAEA,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,sIAAA,CAAA,UAAO,CAAC,MAAM;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,kBAAkB;YAChB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,sIAAA,CAAA,UAAO,CAAC,gBAAgB;gBAC9B,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,iCAAiC;QACjC,2EAA2E;QAC3E,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,UAAU,MAAM,sIAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;gBAC/C,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAK;gBACpC,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,sIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAC7B,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAK;YACtC,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBAErC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;QACtB;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,cAAc,EACd,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layouts/dashboard-layout.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Layout\n// Main layout cho dashboard với navigation và authentication\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Typography,\n  Space,\n  Button,\n  Badge,\n  Spin,\n  Alert\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SyncOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\nimport { useAuth, usePermissions } from '@/stores/auth-store';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD LAYOUT COMPONENT\n// ============================================================================\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, isLoading, logout, getProfile } = useAuth();\n  const permissions = usePermissions();\n\n  const [collapsed, setCollapsed] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Check authentication và load profile\n  useEffect(() => {\n    if (!isAuthenticated && !isLoading) {\n      router.push('/auth/login');\n      return;\n    }\n\n    if (isAuthenticated && !user) {\n      getProfile().catch(() => {\n        router.push('/auth/login');\n      });\n    }\n  }, [isAuthenticated, isLoading, user, router, getProfile]);\n\n  // ========================================================================\n  // MENU CONFIGURATION\n  // ========================================================================\n\n  const getMenuItems = () => {\n    const items = [\n      {\n        key: '/dashboard',\n        icon: <DashboardOutlined />,\n        label: 'Dashboard',\n      }\n    ];\n\n    // User System Management - chỉ admin\n    if (permissions.canManageUsers) {\n      items.push({\n        key: '/dashboard/system-users',\n        icon: <UserOutlined />,\n        label: 'User System',\n      });\n\n      // User Manager (RegisteredUser) - chỉ admin\n      items.push({\n        key: '/dashboard/user-manager',\n        icon: <TeamOutlined />,\n        label: 'User Manager',\n      });\n    }\n\n    // Sports Data Management\n    if (permissions.canManageLeagues) {\n      items.push({\n        key: '/dashboard/leagues',\n        icon: <TrophyOutlined />,\n        label: 'Quản lý Leagues',\n      });\n    }\n\n    if (permissions.canManageTeams) {\n      items.push({\n        key: '/dashboard/teams',\n        icon: <TeamOutlined />,\n        label: 'Quản lý Teams',\n      });\n    }\n\n    if (permissions.canManageFixtures) {\n      items.push({\n        key: '/dashboard/fixtures',\n        icon: <CalendarOutlined />,\n        label: 'Quản lý Fixtures',\n      });\n    }\n\n    // Analytics\n    if (permissions.canViewAnalytics) {\n      items.push({\n        key: '/dashboard/analytics',\n        icon: <BarChartOutlined />,\n        label: 'Analytics',\n      });\n    }\n\n    // Sync Operations\n    if (permissions.canSync) {\n      items.push({\n        key: '/dashboard/sync',\n        icon: <SyncOutlined />,\n        label: 'Sync Operations',\n      });\n    }\n\n    return items;\n  };\n\n  // ========================================================================\n  // USER DROPDOWN MENU\n  // ========================================================================\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Thông tin cá nhân',\n      onClick: () => router.push('/dashboard/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Cài đặt',\n      onClick: () => router.push('/dashboard/settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Đăng xuất',\n      onClick: () => {\n        logout();\n        router.push('/auth/login');\n      },\n    },\n  ];\n\n  // ========================================================================\n  // HANDLERS\n  // ========================================================================\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    router.push(key);\n  };\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // ========================================================================\n  // LOADING STATE\n  // ========================================================================\n\n  if (!mounted || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Spin size=\"large\" tip=\"Đang tải...\" />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // UNAUTHENTICATED STATE\n  // ========================================================================\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Alert\n          message=\"Chưa đăng nhập\"\n          description=\"Vui lòng đăng nhập để tiếp tục\"\n          type=\"warning\"\n          showIcon\n        />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* Sidebar */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        theme=\"dark\"\n        width={256}\n        className=\"shadow-lg\"\n      >\n        {/* Logo */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-700\">\n          <Space>\n            <ApiOutlined className=\"text-white text-xl\" />\n            {!collapsed && (\n              <Title level={4} className=\"text-white m-0\">\n                CMS\n              </Title>\n            )}\n          </Space>\n        </div>\n\n        {/* Navigation Menu */}\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          items={getMenuItems()}\n          onClick={handleMenuClick}\n          className=\"border-r-0\"\n        />\n      </Sider>\n\n      {/* Main Layout */}\n      <Layout>\n        {/* Header */}\n        <Header className=\"bg-white shadow-sm px-4 flex items-center justify-between\">\n          {/* Left side */}\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={toggleCollapsed}\n              className=\"text-lg\"\n            />\n            <Title level={4} className=\"m-0\">\n              APISportsGame CMS\n            </Title>\n          </Space>\n\n          {/* Right side */}\n          <Space size=\"middle\">\n            {/* Notifications */}\n            <Badge count={0} showZero={false}>\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            {/* User Info */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar\n                  size=\"small\"\n                  icon={<UserOutlined />}\n                  className=\"bg-blue-500\"\n                />\n                <div className=\"text-left\">\n                  <div className=\"text-sm font-medium\">\n                    {user?.username}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {permissions.isSystemUser && `${(user as any)?.role}`}\n                    {permissions.isRegisteredUser && `${(user as any)?.tier}`}\n                  </div>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        {/* Content */}\n        <Content className=\"p-6 bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA,uCAAuC;AACvC,6DAA6D;AAE7D;AACA;AA4BA;AA3BA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAoCA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAU3B,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvE,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,WAAW;YAClC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,mBAAmB,CAAC,MAAM;YAC5B,aAAa,KAAK,CAAC;gBACjB,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;QAAM;QAAQ;KAAW;IAEzD,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,eAAe;QACnB,MAAM,QAAQ;YACZ;gBACE,KAAK;gBACL,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;gBACxB,OAAO;YACT;SACD;QAED,qCAAqC;QACrC,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;YAEA,4CAA4C;YAC5C,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;gBACrB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,iBAAiB,EAAE;YACjC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,YAAY;QACZ,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;KACD;IAED,2EAA2E;IAC3E,WAAW;IACX,2EAA2E;IAE3E,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,IAAI,CAAC,WAAW,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;gBAAC,MAAK;gBAAQ,KAAI;;;;;;;;;;;IAG7B;IAEA,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;;;;;;;;;;;IAIhB;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,8OAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAM;gBACN,OAAO;gBACP,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,gNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,CAAC,2BACA,8OAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAiB;;;;;;;;;;;;;;;;;kCAQlD,8OAAC,8KAAA,CAAA,OAAI;wBACH,OAAM;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAKd,8OAAC,kLAAA,CAAA,SAAM;;kCAEL,8OAAC;wBAAO,WAAU;;0CAEhB,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,8OAAC,8NAAA,CAAA,qBAAkB;;;;mEAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAM;;;;;;;;;;;;0CAMnC,8OAAC,gMAAA,CAAA,QAAK;gCAAC,MAAK;;kDAEV,8OAAC,gLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,UAAU;kDACzB,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAKd,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,WAAU;wCACV,KAAK;kDAEL,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kLAAA,CAAA,SAAM;oDACL,MAAK;oDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oDACnB,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,MAAM;;;;;;sEAET,8OAAC;4DAAI,WAAU;;gEACZ,YAAY,YAAY,IAAI,GAAI,MAAc,MAAM;gEACpD,YAAY,gBAAgB,IAAI,GAAI,MAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrE,8OAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Home Page\n// Main dashboard với statistics và overview\n\nimport React from 'react';\nimport { \n  Row, \n  Col, \n  Card, \n  Statistic, \n  Typography, \n  Space,\n  <PERSON><PERSON>,\n  Alert,\n  Divider\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  ApiOutlined,\n  SyncOutlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { DashboardLayout } from '@/components/layouts/dashboard-layout';\nimport { usePermissions } from '@/stores/auth-store';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD PAGE COMPONENT\n// ============================================================================\n\nexport default function DashboardPage() {\n  const permissions = usePermissions();\n\n  // ========================================================================\n  // MOCK DATA - sẽ được thay thế bằng real API calls\n  // ========================================================================\n  const stats = {\n    totalUsers: 1250,\n    totalSystemUsers: 15,\n    totalRegisteredUsers: 1235,\n    totalLeagues: 45,\n    totalTeams: 890,\n    totalFixtures: 12500,\n    apiCallsToday: 8750,\n    activeUsers: 125,\n  };\n\n  const recentActivities = [\n    { id: 1, action: 'Sync completed', time: '5 phút trước', type: 'success' },\n    { id: 2, action: 'New user registered', time: '10 phút trước', type: 'info' },\n    { id: 3, action: 'League updated', time: '15 phút trước', type: 'warning' },\n    { id: 4, action: 'Fixture created', time: '20 phút trước', type: 'info' },\n  ];\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Page Header */}\n        <div>\n          <Title level={2} className=\"mb-2\">\n            Dashboard\n          </Title>\n          <Text type=\"secondary\" className=\"text-base\">\n            Chào mừng trở lại, {permissions.currentUser?.username}! \n            Đây là tổng quan hệ thống APISportsGame CMS.\n          </Text>\n        </div>\n\n        {/* User Info Alert */}\n        <Alert\n          message={`Đăng nhập với quyền: ${\n            permissions.isSystemUser \n              ? `SystemUser (${(permissions.currentUser as any)?.role})` \n              : `RegisteredUser (${(permissions.currentUser as any)?.tier})`\n          }`}\n          type=\"info\"\n          showIcon\n          icon={<CheckCircleOutlined />}\n          className=\"mb-6\"\n        />\n\n        {/* Statistics Cards */}\n        <Row gutter={[16, 16]}>\n          {/* Users Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Tổng Users\"\n                value={stats.totalUsers}\n                prefix={<UserOutlined />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                System: {stats.totalSystemUsers} | Registered: {stats.totalRegisteredUsers}\n              </div>\n            </Card>\n          </Col>\n\n          {/* Leagues Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Leagues\"\n                value={stats.totalLeagues}\n                prefix={<TrophyOutlined />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                Active leagues trong hệ thống\n              </div>\n            </Card>\n          </Col>\n\n          {/* Teams Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Teams\"\n                value={stats.totalTeams}\n                prefix={<TeamOutlined />}\n                valueStyle={{ color: '#faad14' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                Đội bóng đã đồng bộ\n              </div>\n            </Card>\n          </Col>\n\n          {/* Fixtures Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Fixtures\"\n                value={stats.totalFixtures}\n                prefix={<CalendarOutlined />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                Trận đấu trong database\n              </div>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* API Usage & Activity */}\n        <Row gutter={[16, 16]}>\n          {/* API Usage */}\n          <Col xs={24} lg={12}>\n            <Card \n              title={\n                <Space>\n                  <ApiOutlined />\n                  API Usage Today\n                </Space>\n              }\n              extra={\n                <Button type=\"link\" size=\"small\">\n                  Xem chi tiết\n                </Button>\n              }\n            >\n              <Statistic\n                value={stats.apiCallsToday}\n                suffix=\"/ 100,000\"\n                valueStyle={{ color: '#1890ff' }}\n              />\n              <div className=\"mt-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span>Active Users</span>\n                  <span className=\"font-medium\">{stats.activeUsers}</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                  <div \n                    className=\"bg-blue-500 h-2 rounded-full\" \n                    style={{ width: `${(stats.apiCallsToday / 100000) * 100}%` }}\n                  ></div>\n                </div>\n              </div>\n            </Card>\n          </Col>\n\n          {/* Recent Activities */}\n          <Col xs={24} lg={12}>\n            <Card \n              title={\n                <Space>\n                  <BarChartOutlined />\n                  Hoạt động gần đây\n                </Space>\n              }\n              extra={\n                <Button type=\"link\" size=\"small\">\n                  Xem tất cả\n                </Button>\n              }\n            >\n              <div className=\"space-y-3\">\n                {recentActivities.map((activity) => (\n                  <div key={activity.id} className=\"flex justify-between items-center\">\n                    <div>\n                      <div className=\"text-sm font-medium\">{activity.action}</div>\n                      <div className=\"text-xs text-gray-500\">{activity.time}</div>\n                    </div>\n                    <div className={`w-2 h-2 rounded-full ${\n                      activity.type === 'success' ? 'bg-green-500' :\n                      activity.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'\n                    }`}></div>\n                  </div>\n                ))}\n              </div>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* Quick Actions */}\n        <Card title=\"Quick Actions\">\n          <Row gutter={[16, 16]}>\n            {permissions.canSync && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  type=\"primary\" \n                  icon={<SyncOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  Sync Data\n                </Button>\n              </Col>\n            )}\n            \n            {permissions.canManageUsers && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  icon={<UserOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  Manage Users\n                </Button>\n              </Col>\n            )}\n            \n            {permissions.canManageLeagues && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  icon={<TrophyOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  Manage Leagues\n                </Button>\n              </Col>\n            )}\n            \n            {permissions.canViewAnalytics && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  icon={<BarChartOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  View Analytics\n                </Button>\n              </Col>\n            )}\n          </Row>\n        </Card>\n\n        {/* System Status */}\n        <Card title=\"System Status\">\n          <Row gutter={[16, 16]}>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <div className=\"text-2xl text-green-500 mb-2\">●</div>\n                <div className=\"font-medium\">API Server</div>\n                <div className=\"text-sm text-gray-500\">Online</div>\n              </div>\n            </Col>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <div className=\"text-2xl text-green-500 mb-2\">●</div>\n                <div className=\"font-medium\">Database</div>\n                <div className=\"text-sm text-gray-500\">Connected</div>\n              </div>\n            </Col>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <div className=\"text-2xl text-yellow-500 mb-2\">●</div>\n                <div className=\"font-medium\">Sync Service</div>\n                <div className=\"text-sm text-gray-500\">Running</div>\n              </div>\n            </Col>\n          </Row>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AA2BA;AACA;AAtBA;AAAA;AAWA;AAXA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAAA;AAXA;AAWA;AAXA;AAWA;AAAA;AAjBA;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAMnB,SAAS;IACtB,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,2EAA2E;IAC3E,mDAAmD;IACnD,2EAA2E;IAC3E,MAAM,QAAQ;QACZ,YAAY;QACZ,kBAAkB;QAClB,sBAAsB;QACtB,cAAc;QACd,YAAY;QACZ,eAAe;QACf,eAAe;QACf,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB;YAAE,IAAI;YAAG,QAAQ;YAAkB,MAAM;YAAgB,MAAM;QAAU;QACzE;YAAE,IAAI;YAAG,QAAQ;YAAuB,MAAM;YAAiB,MAAM;QAAO;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAkB,MAAM;YAAiB,MAAM;QAAU;QAC1E;YAAE,IAAI;YAAG,QAAQ;YAAmB,MAAM;YAAiB,MAAM;QAAO;KACzE;IAED,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAC3E,qBACE,8OAAC,oJAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAO;;;;;;sCAGlC,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAY;gCACvB,YAAY,WAAW,EAAE;gCAAS;;;;;;;;;;;;;8BAM1D,8OAAC,gLAAA,CAAA,QAAK;oBACJ,SAAS,CAAC,qBAAqB,EAC7B,YAAY,YAAY,GACpB,CAAC,YAAY,EAAG,YAAY,WAAW,EAAU,KAAK,CAAC,CAAC,GACxD,CAAC,gBAAgB,EAAG,YAAY,WAAW,EAAU,KAAK,CAAC,CAAC,EAChE;oBACF,MAAK;oBACL,QAAQ;oBACR,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;oBAC1B,WAAU;;;;;;8BAIZ,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCAEnB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;;kDACH,8OAAC,wLAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,UAAU;wCACvB,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;4CAA6B;4CACjC,MAAM,gBAAgB;4CAAC;4CAAgB,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;sCAMhF,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;;kDACH,8OAAC,wLAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,YAAY;wCACzB,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACvB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAOhD,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;;kDACH,8OAAC,wLAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,UAAU;wCACvB,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAOhD,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;;kDACH,8OAAC,wLAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,aAAa;wCAC1B,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCACzB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;8BAQlD,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCAEnB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;gCACH,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,gNAAA,CAAA,cAAW;;;;;wCAAG;;;;;;;gCAInB,qBACE,8OAAC,kMAAA,CAAA,SAAM;oCAAC,MAAK;oCAAO,MAAK;8CAAQ;;;;;;;kDAKnC,8OAAC,wLAAA,CAAA,YAAS;wCACR,OAAO,MAAM,aAAa;wCAC1B,QAAO;wCACP,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAAe,MAAM,WAAW;;;;;;;;;;;;0DAElD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,AAAC,MAAM,aAAa,GAAG,SAAU,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrE,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;gCACH,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAAG;;;;;;;gCAIxB,qBACE,8OAAC,kMAAA,CAAA,SAAM;oCAAC,MAAK;oCAAO,MAAK;8CAAQ;;;;;;0CAKnC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAuB,SAAS,MAAM;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAyB,SAAS,IAAI;;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EACpC,SAAS,IAAI,KAAK,YAAY,iBAC9B,SAAS,IAAI,KAAK,YAAY,kBAAkB,eAChD;;;;;;;2CARM,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;8BAiB/B,8OAAC,8KAAA,CAAA,OAAI;oBAAC,OAAM;8BACV,cAAA,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;;4BAClB,YAAY,OAAO,kBAClB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;4BAMJ,YAAY,cAAc,kBACzB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;4BAMJ,YAAY,gBAAgB,kBAC3B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oCACrB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;4BAMJ,YAAY,gBAAgB,kBAC3B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oCACvB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC,8KAAA,CAAA,OAAI;oBAAC,OAAM;8BACV,cAAA,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;;0CACnB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAG3C,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAG3C,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,8OAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD"}}, {"offset": {"line": 2272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}