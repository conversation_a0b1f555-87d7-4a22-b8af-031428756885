{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/api/lib/proxy.ts"], "sourcesContent": ["// APISportsGame CMS - API Proxy Utility\n// Utility functions cho API proxy routes\n\nimport { NextRequest, NextResponse } from 'next/server';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface ProxyConfig {\n  baseUrl: string;\n  timeout?: number;\n  headers?: Record<string, string>;\n}\n\nexport interface ProxyOptions {\n  method?: string;\n  body?: any;\n  headers?: Record<string, string>;\n  searchParams?: URLSearchParams;\n}\n\n// ============================================================================\n// PROXY UTILITY\n// ============================================================================\n\nexport class ApiProxy {\n  private baseUrl: string;\n  private timeout: number;\n  private defaultHeaders: Record<string, string>;\n\n  constructor(config: ProxyConfig) {\n    this.baseUrl = config.baseUrl;\n    this.timeout = config.timeout || 10000;\n    this.defaultHeaders = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      ...config.headers,\n    };\n  }\n\n  /**\n   * Proxy request đến backend API\n   */\n  async proxyRequest(\n    endpoint: string,\n    options: ProxyOptions = {}\n  ): Promise<NextResponse> {\n    try {\n      const url = new URL(endpoint, this.baseUrl);\n\n      // Add search params nếu có\n      if (options.searchParams) {\n        options.searchParams.forEach((value, key) => {\n          url.searchParams.append(key, value);\n        });\n      }\n\n      // Merge headers và normalize case\n      const requestHeaders: Record<string, string> = {\n        ...this.defaultHeaders,\n      };\n\n      // Add options headers với normalized keys\n      if (options.headers) {\n        Object.entries(options.headers).forEach(([key, value]) => {\n          const normalizedKey = key.toLowerCase();\n          if (normalizedKey === 'content-type') {\n            requestHeaders['Content-Type'] = value;\n          } else if (normalizedKey === 'accept') {\n            requestHeaders['Accept'] = value;\n          } else if (normalizedKey === 'authorization') {\n            requestHeaders['Authorization'] = value;\n          }\n        });\n      }\n\n      let requestBody: string | undefined;\n\n      // Prepare body cho POST/PUT/PATCH requests\n      if (options.body && ['POST', 'PUT', 'PATCH'].includes(options.method || 'GET')) {\n        requestBody = typeof options.body === 'string'\n          ? options.body\n          : JSON.stringify(options.body);\n\n        // Không set Content-Length manually, fetch sẽ tự động handle\n      }\n\n      const requestInit: RequestInit = {\n        method: options.method || 'GET',\n        headers: requestHeaders,\n        body: requestBody,\n        signal: AbortSignal.timeout(this.timeout),\n      };\n\n      console.log(`🔄 Proxying ${requestInit.method} ${url.toString()}`);\n      console.log('📦 Request body:', requestBody);\n      console.log('📋 Request headers:', requestHeaders);\n      console.log('🔧 Full request init:', JSON.stringify(requestInit, null, 2));\n\n      const response = await fetch(url.toString(), requestInit);\n\n      // Get response data\n      const contentType = response.headers.get('content-type');\n      let data;\n\n      if (contentType?.includes('application/json')) {\n        data = await response.json();\n      } else {\n        data = await response.text();\n      }\n\n      console.log(`✅ Proxy response: ${response.status} ${response.statusText}`);\n      console.log('📥 Response data:', data);\n\n      // Return NextResponse với same status và data\n      return NextResponse.json(data, {\n        status: response.status,\n        statusText: response.statusText,\n        headers: {\n          'Content-Type': contentType || 'application/json',\n        },\n      });\n\n    } catch (error: any) {\n      console.error('❌ Proxy error:', error);\n\n      // Handle timeout\n      if (error.name === 'TimeoutError') {\n        return NextResponse.json(\n          {\n            error: 'Request timeout',\n            message: 'The request took too long to complete',\n            statusCode: 408\n          },\n          { status: 408 }\n        );\n      }\n\n      // Handle network errors\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\n        return NextResponse.json(\n          {\n            error: 'Network error',\n            message: 'Unable to connect to the API server',\n            statusCode: 503\n          },\n          { status: 503 }\n        );\n      }\n\n      // Generic error\n      return NextResponse.json(\n        {\n          error: 'Proxy error',\n          message: error.message || 'An unknown error occurred',\n          statusCode: 500\n        },\n        { status: 500 }\n      );\n    }\n  }\n\n  /**\n   * Extract request data từ NextRequest\n   */\n  async extractRequestData(request: NextRequest): Promise<ProxyOptions> {\n    const method = request.method;\n    const url = new URL(request.url);\n    const searchParams = url.searchParams;\n\n    // Extract headers (loại bỏ NextJS internal headers và browser headers)\n    const headers: Record<string, string> = {};\n    const allowedHeaders = ['content-type', 'accept', 'authorization'];\n\n    request.headers.forEach((value, key) => {\n      const lowerKey = key.toLowerCase();\n      if (allowedHeaders.includes(lowerKey)) {\n        headers[key] = value;\n      }\n    });\n\n    // Extract body cho POST/PUT/PATCH\n    let body;\n    if (['POST', 'PUT', 'PATCH'].includes(method)) {\n      const contentType = request.headers.get('content-type');\n      if (contentType?.includes('application/json')) {\n        body = await request.json();\n      } else {\n        body = await request.text();\n      }\n    }\n\n    return {\n      method,\n      headers,\n      body,\n      searchParams,\n    };\n  }\n}\n\n// ============================================================================\n// SINGLETON INSTANCE\n// ============================================================================\n\nexport const apiProxy = new ApiProxy({\n  baseUrl: process.env.API_BASE_URL || 'http://localhost:3000',\n  timeout: 10000,\n});\n\n// ============================================================================\n// HELPER FUNCTIONS\n// ============================================================================\n\n/**\n * Create proxy route handler\n */\nexport function createProxyHandler(endpoint: string) {\n  return async function handler(request: NextRequest) {\n    try {\n      const options = await apiProxy.extractRequestData(request);\n      return await apiProxy.proxyRequest(endpoint, options);\n    } catch (error) {\n      console.error('Proxy handler error:', error);\n      return NextResponse.json(\n        { error: 'Internal proxy error' },\n        { status: 500 }\n      );\n    }\n  };\n}\n\n/**\n * Create dynamic proxy handler cho [...slug] routes\n */\nexport function createDynamicProxyHandler(basePath: string) {\n  return async function handler(\n    request: NextRequest,\n    { params }: { params: { slug: string[] } }\n  ) {\n    try {\n      const endpoint = `${basePath}/${params.slug.join('/')}`;\n      const options = await apiProxy.extractRequestData(request);\n      return await apiProxy.proxyRequest(endpoint, options);\n    } catch (error) {\n      console.error('Dynamic proxy handler error:', error);\n      return NextResponse.json(\n        { error: 'Internal proxy error' },\n        { status: 500 }\n      );\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,yCAAyC;;;;;;;AAEzC;;AAuBO,MAAM;IACH,QAAgB;IAChB,QAAgB;IAChB,eAAuC;IAE/C,YAAY,MAAmB,CAAE;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,IAAI;QACjC,IAAI,CAAC,cAAc,GAAG;YACpB,gBAAgB;YAChB,UAAU;YACV,GAAG,OAAO,OAAO;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,aACJ,QAAgB,EAChB,UAAwB,CAAC,CAAC,EACH;QACvB,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO;YAE1C,2BAA2B;YAC3B,IAAI,QAAQ,YAAY,EAAE;gBACxB,QAAQ,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO;oBACnC,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;gBAC/B;YACF;YAEA,kCAAkC;YAClC,MAAM,iBAAyC;gBAC7C,GAAG,IAAI,CAAC,cAAc;YACxB;YAEA,0CAA0C;YAC1C,IAAI,QAAQ,OAAO,EAAE;gBACnB,OAAO,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBACnD,MAAM,gBAAgB,IAAI,WAAW;oBACrC,IAAI,kBAAkB,gBAAgB;wBACpC,cAAc,CAAC,eAAe,GAAG;oBACnC,OAAO,IAAI,kBAAkB,UAAU;wBACrC,cAAc,CAAC,SAAS,GAAG;oBAC7B,OAAO,IAAI,kBAAkB,iBAAiB;wBAC5C,cAAc,CAAC,gBAAgB,GAAG;oBACpC;gBACF;YACF;YAEA,IAAI;YAEJ,2CAA2C;YAC3C,IAAI,QAAQ,IAAI,IAAI;gBAAC;gBAAQ;gBAAO;aAAQ,CAAC,QAAQ,CAAC,QAAQ,MAAM,IAAI,QAAQ;gBAC9E,cAAc,OAAO,QAAQ,IAAI,KAAK,WAClC,QAAQ,IAAI,GACZ,KAAK,SAAS,CAAC,QAAQ,IAAI;YAE/B,6DAA6D;YAC/D;YAEA,MAAM,cAA2B;gBAC/B,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,SAAS;gBACT,MAAM;gBACN,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,OAAO;YAC1C;YAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,IAAI,QAAQ,IAAI;YACjE,QAAQ,GAAG,CAAC,oBAAoB;YAChC,QAAQ,GAAG,CAAC,uBAAuB;YACnC,QAAQ,GAAG,CAAC,yBAAyB,KAAK,SAAS,CAAC,aAAa,MAAM;YAEvE,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAE7C,oBAAoB;YACpB,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI;YAEJ,IAAI,aAAa,SAAS,qBAAqB;gBAC7C,OAAO,MAAM,SAAS,IAAI;YAC5B,OAAO;gBACL,OAAO,MAAM,SAAS,IAAI;YAC5B;YAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YACzE,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,8CAA8C;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;gBAC7B,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS;oBACP,gBAAgB,eAAe;gBACjC;YACF;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kBAAkB;YAEhC,iBAAiB;YACjB,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,OAAO;oBACP,SAAS;oBACT,YAAY;gBACd,GACA;oBAAE,QAAQ;gBAAI;YAElB;YAEA,wBAAwB;YACxB,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,OAAO;oBACP,SAAS;oBACT,YAAY;gBACd,GACA;oBAAE,QAAQ;gBAAI;YAElB;YAEA,gBAAgB;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,MAAM,OAAO,IAAI;gBAC1B,YAAY;YACd,GACA;gBAAE,QAAQ;YAAI;QAElB;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAoB,EAAyB;QACpE,MAAM,SAAS,QAAQ,MAAM;QAC7B,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,eAAe,IAAI,YAAY;QAErC,uEAAuE;QACvE,MAAM,UAAkC,CAAC;QACzC,MAAM,iBAAiB;YAAC;YAAgB;YAAU;SAAgB;QAElE,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,MAAM,WAAW,IAAI,WAAW;YAChC,IAAI,eAAe,QAAQ,CAAC,WAAW;gBACrC,OAAO,CAAC,IAAI,GAAG;YACjB;QACF;QAEA,kCAAkC;QAClC,IAAI;QACJ,IAAI;YAAC;YAAQ;YAAO;SAAQ,CAAC,QAAQ,CAAC,SAAS;YAC7C,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;YACxC,IAAI,aAAa,SAAS,qBAAqB;gBAC7C,OAAO,MAAM,QAAQ,IAAI;YAC3B,OAAO;gBACL,OAAO,MAAM,QAAQ,IAAI;YAC3B;QACF;QAEA,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;AACF;AAMO,MAAM,WAAW,IAAI,SAAS;IACnC,SAAS,QAAQ,GAAG,CAAC,YAAY,IAAI;IACrC,SAAS;AACX;AASO,SAAS,mBAAmB,QAAgB;IACjD,OAAO,eAAe,QAAQ,OAAoB;QAChD,IAAI;YACF,MAAM,UAAU,MAAM,SAAS,kBAAkB,CAAC;YAClD,OAAO,MAAM,SAAS,YAAY,CAAC,UAAU;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAKO,SAAS,0BAA0B,QAAgB;IACxD,OAAO,eAAe,QACpB,OAAoB,EACpB,EAAE,MAAM,EAAkC;QAE1C,IAAI;YACF,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;YACvD,MAAM,UAAU,MAAM,SAAS,kBAAkB,CAAC;YAClD,OAAO,MAAM,SAAS,YAAY,CAAC,UAAU;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF"}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/api/auth/login/route.ts"], "sourcesContent": ["// APISportsGame CMS - Auth Login Proxy Route\n// Proxy route cho system authentication\n\nimport { NextRequest } from 'next/server';\nimport { createProxyHandler } from '../../lib/proxy';\n\n// ============================================================================\n// AUTH LOGIN PROXY\n// ============================================================================\n\nconst handler = createProxyHandler('/system-auth/login');\n\nexport async function POST(request: NextRequest) {\n  return handler(request);\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,wCAAwC;;;;AAGxC;;AAEA,+EAA+E;AAC/E,mBAAmB;AACnB,+EAA+E;AAE/E,MAAM,UAAU,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD,EAAE;AAE5B,eAAe,KAAK,OAAoB;IAC7C,OAAO,QAAQ;AACjB"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}