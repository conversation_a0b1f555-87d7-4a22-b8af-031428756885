// Simple test cho proxy login
const axios = require('axios');

async function testProxyLogin() {
  try {
    console.log('🔍 Testing proxy login...');
    
    const response = await axios.post('http://localhost:4000/api/auth/login', {
      username: 'admin',
      password: 'admin123456'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Proxy login successful!');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Proxy login failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
}

testProxyLogin();
