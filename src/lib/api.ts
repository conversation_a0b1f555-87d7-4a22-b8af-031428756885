// APISportsGame CMS - API Client
// Base API client setup theo CMS Development Guide

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  SystemUser,
  RegisteredUser,
  League,
  Team,
  Fixture,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  PaginatedResponse,
  ApiError,
  DashboardStats,
  SyncStatus
} from '@/types';

// ============================================================================
// API CONFIGURATION
// ============================================================================

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api';

// API Endpoints - Sử dụng proxy routes
export const endpoints = {
  // Authentication endpoints
  auth: {
    login: '/auth/login',
    profile: '/auth/profile',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    adminRegister: '/auth/admin/register'
  },
  // RegisteredUser endpoints
  users: {
    register: '/users/register',
    login: '/users/login',
    verifyEmail: '/users/verify-email',
    profile: '/users/profile',
    apiUsage: '/users/api-usage'
  },
  // League endpoints
  leagues: {
    list: '/football/leagues',
    byExternalId: (externalId: number) => `/football/leagues/${externalId}`,
    create: '/football/leagues',
    update: (id: number) => `/football/leagues/${id}`,
    delete: (id: number) => `/football/leagues/${id}`
  },
  // Team endpoints
  teams: {
    list: '/football/teams',
    statistics: '/football/teams/statistics',
    byExternalId: (externalId: number) => `/football/teams/${externalId}`,
    create: '/football/teams',
    update: (id: number) => `/football/teams/${id}`,
    delete: (id: number) => `/football/teams/${id}`
  },
  // Fixture endpoints
  fixtures: {
    list: '/football/fixtures',
    upcomingAndLive: '/football/fixtures/upcoming-and-live',
    byExternalId: (externalId: number) => `/football/fixtures/${externalId}`,
    statistics: (externalId: number) => `/football/fixtures/statistics/${externalId}`,
    schedules: (teamId: number) => `/football/fixtures/schedules/${teamId}`,
    create: '/football/fixtures',
    update: (externalId: number) => `/football/fixtures/${externalId}`,
    delete: (id: number) => `/football/fixtures/${id}`
  },
  // Sync endpoints
  sync: {
    fixtures: '/football/fixtures/sync/fixtures',
    daily: '/football/fixtures/sync/daily',
    status: '/football/fixtures/sync/status'
  },

  // Admin endpoints - theo CMS_DEVELOPMENT_GUIDE.md
  admin: {
    users: '/admin/users',
    tiers: '/admin/tiers/statistics',
    resetUsage: '/admin/reset-api-usage',
    checkWarnings: '/admin/check-usage-warnings',
    upgradeUser: (userId: number) => `/admin/users/${userId}/upgrade-tier`,
    downgradeUser: (userId: number) => `/admin/users/${userId}/downgrade-tier`,
    extendSubscription: (userId: number) => `/admin/users/${userId}/extend-subscription`,
    userSubscription: (userId: number) => `/admin/users/${userId}/subscription`,
    approachingLimits: '/admin/users/approaching-limits'
  },

  // RegisteredUser endpoints
  users: {
    register: '/users/register',
    login: '/users/login',
    verifyEmail: '/users/verify-email',
    profile: '/users/profile',
    apiUsage: '/users/api-usage'
  },

  // Broadcast links
  broadcastLinks: {
    list: '/broadcast-links',
    create: '/broadcast-links',
    byFixture: (fixtureId: number) => `/broadcast-links/fixture/${fixtureId}`,
    update: (id: number) => `/broadcast-links/${id}`,
    delete: (id: number) => `/broadcast-links/${id}`
  }
};

// ============================================================================
// AXIOS INSTANCE SETUP
// ============================================================================

class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - thêm JWT token
    this.instance.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor - xử lý errors
    this.instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors - token expired
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshToken();
            const token = this.getToken();
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            this.logout();
            window.location.href = '/auth/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token');
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token);
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  private async refreshToken(): Promise<void> {
    const refreshToken = typeof window !== 'undefined'
      ? localStorage.getItem('refresh_token')
      : null;

    if (!refreshToken) {
      throw new Error('No refresh token');
    }

    const response = await this.instance.post(endpoints.auth.refresh, {
      refresh_token: refreshToken
    });

    const { accessToken } = response.data;
    this.setToken(accessToken);
  }

  private logout(): void {
    this.removeToken();
  }

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.instance.post<LoginResponse>(
      endpoints.auth.login,
      credentials
    );

    const { accessToken, refreshToken } = response.data;
    this.setToken(accessToken);

    if (typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', refreshToken);
    }

    return response.data;
  }

  async getProfile(): Promise<SystemUser | RegisteredUser> {
    const response = await this.instance.get<SystemUser | RegisteredUser>(
      endpoints.auth.profile
    );
    return response.data;
  }

  async logoutUser(): Promise<void> {
    try {
      await this.instance.post(endpoints.auth.logout);
    } finally {
      this.logout();
    }
  }

  async createSystemUser(userData: any): Promise<SystemUser> {
    const response = await this.instance.post<SystemUser>(
      endpoints.auth.adminRegister,
      userData
    );
    return response.data;
  }

  // ============================================================================
  // LEAGUE METHODS
  // ============================================================================

  async getLeagues(params?: any): Promise<PaginatedResponse<League>> {
    const response = await this.instance.get<PaginatedResponse<League>>(
      endpoints.leagues.list,
      { params }
    );
    return response.data;
  }

  async getLeague(externalId: number): Promise<League> {
    const response = await this.instance.get<League>(
      endpoints.leagues.byExternalId(externalId)
    );
    return response.data;
  }

  async createLeague(leagueData: any): Promise<League> {
    const response = await this.instance.post<League>(
      endpoints.leagues.create,
      leagueData
    );
    return response.data;
  }

  async updateLeague(id: number, leagueData: any): Promise<League> {
    const response = await this.instance.put<League>(
      endpoints.leagues.update(id),
      leagueData
    );
    return response.data;
  }

  async deleteLeague(id: number): Promise<void> {
    await this.instance.delete(endpoints.leagues.delete(id));
  }

  // ============================================================================
  // TEAM METHODS
  // ============================================================================

  async getTeams(params?: any): Promise<PaginatedResponse<Team>> {
    const response = await this.instance.get<PaginatedResponse<Team>>(
      endpoints.teams.list,
      { params }
    );
    return response.data;
  }

  async getTeam(externalId: number): Promise<Team> {
    const response = await this.instance.get<Team>(
      endpoints.teams.byExternalId(externalId)
    );
    return response.data;
  }

  async createTeam(teamData: any): Promise<Team> {
    const response = await this.instance.post<Team>(
      endpoints.teams.create,
      teamData
    );
    return response.data;
  }

  async updateTeam(id: number, teamData: any): Promise<Team> {
    const response = await this.instance.put<Team>(
      endpoints.teams.update(id),
      teamData
    );
    return response.data;
  }

  async deleteTeam(id: number): Promise<void> {
    await this.instance.delete(endpoints.teams.delete(id));
  }

  // ============================================================================
  // FIXTURE METHODS
  // ============================================================================

  async getFixtures(params?: any): Promise<PaginatedResponse<Fixture>> {
    const response = await this.instance.get<PaginatedResponse<Fixture>>(
      endpoints.fixtures.list,
      { params }
    );
    return response.data;
  }

  async getFixture(externalId: number): Promise<Fixture> {
    const response = await this.instance.get<Fixture>(
      endpoints.fixtures.byExternalId(externalId)
    );
    return response.data;
  }

  async getUpcomingAndLiveFixtures(): Promise<Fixture[]> {
    const response = await this.instance.get<Fixture[]>(
      endpoints.fixtures.upcomingAndLive
    );
    return response.data;
  }

  async getFixtureStatistics(externalId: number): Promise<any> {
    const response = await this.instance.get<any>(
      endpoints.fixtures.statistics(externalId)
    );
    return response.data;
  }

  async getTeamSchedule(teamId: number): Promise<Fixture[]> {
    const response = await this.instance.get<Fixture[]>(
      endpoints.fixtures.schedules(teamId)
    );
    return response.data;
  }

  // ============================================================================
  // SYNC METHODS
  // ============================================================================

  async syncFixtures(): Promise<void> {
    await this.instance.get(endpoints.sync.fixtures);
  }

  async syncDaily(): Promise<void> {
    await this.instance.get(endpoints.sync.daily);
  }

  async getSyncStatus(): Promise<any> {
    const response = await this.instance.get<any>(
      endpoints.sync.status
    );
    return response.data;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
