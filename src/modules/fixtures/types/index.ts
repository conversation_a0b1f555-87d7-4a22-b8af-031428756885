// APISportsGame CMS - Fixtures Module Types
// Fixture related types theo CMS_DEVELOPMENT_GUIDE.md

import { BaseEntity, BaseFilters } from '@/shared/types/common';

// ============================================================================
// FIXTURE ENTITY TYPES
// ============================================================================

// Fixture interface từ CMS_DEVELOPMENT_GUIDE.md
export interface Fixture extends BaseEntity {
  apiFootballId: number;
  referee: string;
  timezone: string;
  date: Date;
  timestamp: number;
  
  // Venue
  venueId: number;
  venueName: string;
  venueCity: string;
  
  // Status
  statusLong: string;
  statusShort: string;
  elapsed: number;
  
  // League
  leagueId: number;
  leagueName: string;
  leagueSeason: number;
  leagueRound: string;
  
  // Teams
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  
  // Score
  goalsHome: number;
  goalsAway: number;
  scoreHalftimeHome: number;
  scoreHalftimeAway: number;
  scoreFulltimeHome: number;
  scoreFulltimeAway: number;
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateFixtureForm {
  apiFootballId: number;
  referee?: string;
  timezone: string;
  date: Date;
  timestamp: number;
  
  // Venue
  venueId: number;
  venueName: string;
  venueCity: string;
  
  // Status
  statusLong: string;
  statusShort: string;
  elapsed?: number;
  
  // League
  leagueId: number;
  leagueName: string;
  leagueSeason: number;
  leagueRound: string;
  
  // Teams
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  
  // Score
  goalsHome?: number;
  goalsAway?: number;
  scoreHalftimeHome?: number;
  scoreHalftimeAway?: number;
  scoreFulltimeHome?: number;
  scoreFulltimeAway?: number;
}

export interface UpdateFixtureForm {
  apiFootballId?: number;
  referee?: string;
  timezone?: string;
  date?: Date;
  timestamp?: number;
  
  // Venue
  venueId?: number;
  venueName?: string;
  venueCity?: string;
  
  // Status
  statusLong?: string;
  statusShort?: string;
  elapsed?: number;
  
  // League
  leagueId?: number;
  leagueName?: string;
  leagueSeason?: number;
  leagueRound?: string;
  
  // Teams
  homeTeamId?: number;
  homeTeamName?: string;
  homeTeamLogo?: string;
  awayTeamId?: number;
  awayTeamName?: string;
  awayTeamLogo?: string;
  
  // Score
  goalsHome?: number;
  goalsAway?: number;
  scoreHalftimeHome?: number;
  scoreHalftimeAway?: number;
  scoreFulltimeHome?: number;
  scoreFulltimeAway?: number;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface FixtureFilters extends BaseFilters {
  leagueId?: number;
  homeTeamId?: number;
  awayTeamId?: number;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  season?: number;
  round?: string;
  live?: boolean;
}

// ============================================================================
// API TYPES
// ============================================================================

export interface FixtureApiResponse {
  data: Fixture[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface SingleFixtureApiResponse {
  data: Fixture;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface FixtureTableProps {
  fixtures: Fixture[];
  loading?: boolean;
  onEdit?: (fixture: Fixture) => void;
  onDelete?: (fixture: Fixture) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onSync?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: FixtureFilters;
  onFiltersChange?: (filters: FixtureFilters) => void;
}

export interface FixtureFormProps {
  fixture?: Fixture;
  onSubmit: (data: CreateFixtureForm | UpdateFixtureForm) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export interface FixtureCardProps {
  fixture: Fixture;
  onEdit?: (fixture: Fixture) => void;
  onDelete?: (fixture: Fixture) => void;
  onView?: (fixture: Fixture) => void;
  showActions?: boolean;
  showScore?: boolean;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseFixturesOptions {
  filters?: FixtureFilters;
  page?: number;
  limit?: number;
}

export interface UseFixturesReturn {
  fixtures: Fixture[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  refetch: () => void;
}

export interface UseFixtureMutationsReturn {
  createFixture: {
    mutate: (data: CreateFixtureForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  updateFixture: {
    mutate: (id: number, data: UpdateFixtureForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  deleteFixture: {
    mutate: (id: number) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  syncFixtures: {
    mutate: () => Promise<void>;
    loading: boolean;
    error: string | null;
  };
}

// ============================================================================
// SYNC TYPES
// ============================================================================

export interface FixtureSyncStatus {
  isRunning: boolean;
  lastSync: Date | null;
  nextScheduled: Date | null;
  totalSynced: number;
  errors: string[];
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
}

export interface FixtureSyncOptions {
  leagues?: number[];
  teams?: number[];
  dateFrom?: string;
  dateTo?: string;
  forceUpdate?: boolean;
  includeLive?: boolean;
}

// ============================================================================
// EXPORT ALL
// ============================================================================

export type {
  Fixture,
  CreateFixtureForm,
  UpdateFixtureForm,
  FixtureFilters,
  FixtureApiResponse,
  SingleFixtureApiResponse,
  FixtureTableProps,
  FixtureFormProps,
  FixtureCardProps,
  UseFixturesOptions,
  UseFixturesReturn,
  UseFixtureMutationsReturn,
  FixtureSyncStatus,
  FixtureSyncOptions,
};
