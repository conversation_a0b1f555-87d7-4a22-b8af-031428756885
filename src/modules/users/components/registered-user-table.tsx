// APISportsGame CMS - RegisteredUser Table Component
// Table component cho RegisteredUser management với admin operations

'use client';

import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Dropdown,
  Modal,
  Input,
  Select,
  DatePicker,
  Card,
  Row,
  Col,
  Progress,
  Badge,
  Avatar,
  Typography,
  message,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  ExportOutlined,
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CalendarOutlined,
  ApiOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import {
  RegisteredUser,
  RegisteredUserFilters,
  RegisteredUserTier,
  TierUpgradeRequest,
  TierDowngradeRequest,
  ExtendSubscriptionRequest,
} from '../types';
import { formatDate, formatNumber } from '@/shared/utils/format';

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// ============================================================================
// COMPONENT PROPS
// ============================================================================

export interface RegisteredUserTableProps {
  users: RegisteredUser[];
  loading?: boolean;
  onEdit?: (user: RegisteredUser) => void;
  onDelete?: (user: RegisteredUser) => void;
  onUpgradeTier?: (data: TierUpgradeRequest) => void;
  onDowngradeTier?: (data: TierDowngradeRequest) => void;
  onExtendSubscription?: (data: ExtendSubscriptionRequest) => void;
  onViewUsage?: (user: RegisteredUser) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: RegisteredUserFilters;
  onFiltersChange?: (filters: RegisteredUserFilters) => void;
}

// ============================================================================
// REGISTERED USER TABLE COMPONENT
// ============================================================================

export function RegisteredUserTable({
  users,
  loading = false,
  onEdit,
  onDelete,
  onUpgradeTier,
  onDowngradeTier,
  onExtendSubscription,
  onViewUsage,
  onAdd,
  onRefresh,
  onExport,
  pagination,
  filters = {},
  onFiltersChange,
}: RegisteredUserTableProps) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // ========================================================================
  // HELPER FUNCTIONS
  // ========================================================================

  const getTierColor = (tier: RegisteredUserTier) => {
    const colors = {
      free: 'default',
      premium: 'gold',
      enterprise: 'purple',
    };
    return colors[tier];
  };

  const getApiUsageStatus = (used: number, limit: number | null) => {
    if (!limit) return { percentage: 0, status: 'normal' };
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return { percentage, status: 'danger' };
    if (percentage >= 70) return { percentage, status: 'warning' };
    return { percentage, status: 'normal' };
  };

  const isSubscriptionExpiring = (endDate: Date | null) => {
    if (!endDate) return false;
    const now = new Date();
    const expiry = new Date(endDate);
    const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  };

  const isSubscriptionExpired = (endDate: Date | null) => {
    if (!endDate) return false;
    return new Date(endDate) < new Date();
  };

  // ========================================================================
  // COLUMN DEFINITIONS
  // ========================================================================

  const columns: ColumnsType<RegisteredUser> = [
    {
      title: 'User',
      dataIndex: 'username',
      key: 'username',
      render: (username: string, record: RegisteredUser) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div className="font-medium">{username}</div>
            <Text type="secondary" className="text-xs">
              {record.email}
            </Text>
          </div>
        </Space>
      ),
      sorter: true,
    },
    {
      title: 'Tier',
      dataIndex: 'tier',
      key: 'tier',
      render: (tier: RegisteredUserTier) => (
        <Tag color={getTierColor(tier)}>
          {tier.toUpperCase()}
        </Tag>
      ),
      filters: [
        { text: 'Free', value: 'free' },
        { text: 'Premium', value: 'premium' },
        { text: 'Enterprise', value: 'enterprise' },
      ],
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record: RegisteredUser) => (
        <Space direction="vertical" size="small">
          <Tag color={record.isActive ? 'success' : 'error'}>
            {record.isActive ? 'Active' : 'Inactive'}
          </Tag>
          <Tag color={record.isEmailVerified ? 'success' : 'warning'} className="text-xs">
            {record.isEmailVerified ? 'Verified' : 'Unverified'}
          </Tag>
          {record.subscriptionEndDate && (
            <Tag 
              color={
                isSubscriptionExpired(record.subscriptionEndDate) ? 'error' :
                isSubscriptionExpiring(record.subscriptionEndDate) ? 'warning' : 'success'
              }
              className="text-xs"
            >
              {isSubscriptionExpired(record.subscriptionEndDate) ? 'Expired' :
               isSubscriptionExpiring(record.subscriptionEndDate) ? 'Expiring' : 'Active'}
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'API Usage',
      key: 'apiUsage',
      render: (_, record: RegisteredUser) => {
        const { percentage, status } = getApiUsageStatus(record.apiCallsUsed, record.apiCallsLimit);
        const strokeColor = status === 'danger' ? '#ff4d4f' : status === 'warning' ? '#faad14' : '#52c41a';
        
        return (
          <div className="w-32">
            <div className="text-sm mb-1">
              {formatNumber(record.apiCallsUsed)} / {record.apiCallsLimit ? formatNumber(record.apiCallsLimit) : '∞'}
            </div>
            <Progress
              percent={Math.min(percentage, 100)}
              size="small"
              strokeColor={strokeColor}
              showInfo={false}
            />
            {status === 'danger' && (
              <Badge status="error" text="Limit reached" className="text-xs" />
            )}
          </div>
        );
      },
    },
    {
      title: 'Subscription',
      key: 'subscription',
      render: (_, record: RegisteredUser) => {
        if (!record.subscriptionEndDate) {
          return <Text type="secondary">No subscription</Text>;
        }
        
        return (
          <div>
            <div className="text-sm">
              Expires: {formatDate(record.subscriptionEndDate)}
            </div>
            {isSubscriptionExpiring(record.subscriptionEndDate) && (
              <Text type="warning" className="text-xs">
                <WarningOutlined /> Expiring soon
              </Text>
            )}
          </div>
        );
      },
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      render: (date: Date) => (
        <Text type="secondary" className="text-sm">
          {formatDate(date)}
        </Text>
      ),
      sorter: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: RegisteredUser) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: 'Edit User',
                  icon: <EditOutlined />,
                  onClick: () => onEdit?.(record),
                },
                {
                  key: 'usage',
                  label: 'View API Usage',
                  icon: <ApiOutlined />,
                  onClick: () => onViewUsage?.(record),
                },
                {
                  type: 'divider',
                },
                {
                  key: 'upgrade',
                  label: 'Upgrade Tier',
                  icon: <ArrowUpOutlined />,
                  onClick: () => handleUpgradeTier(record),
                },
                {
                  key: 'downgrade',
                  label: 'Downgrade Tier',
                  icon: <ArrowDownOutlined />,
                  onClick: () => handleDowngradeTier(record),
                },
                {
                  key: 'extend',
                  label: 'Extend Subscription',
                  icon: <CalendarOutlined />,
                  onClick: () => handleExtendSubscription(record),
                },
                {
                  type: 'divider',
                },
                {
                  key: 'delete',
                  label: 'Delete User',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
            trigger={['click']}
          >
            <Button type="text" size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleDelete = (user: RegisteredUser) => {
    Modal.confirm({
      title: 'Delete User',
      content: `Are you sure you want to delete user "${user.username}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => onDelete?.(user),
    });
  };

  const handleUpgradeTier = (user: RegisteredUser) => {
    // This would open a modal for tier upgrade
    // For now, just show a simple prompt
    const newTier = user.tier === 'free' ? 'premium' : 'enterprise';
    Modal.confirm({
      title: 'Upgrade Tier',
      content: `Upgrade ${user.username} from ${user.tier} to ${newTier}?`,
      onOk: () => onUpgradeTier?.({
        userId: user.id,
        newTier: newTier as RegisteredUserTier,
      }),
    });
  };

  const handleDowngradeTier = (user: RegisteredUser) => {
    const newTier = user.tier === 'enterprise' ? 'premium' : 'free';
    Modal.confirm({
      title: 'Downgrade Tier',
      content: `Downgrade ${user.username} from ${user.tier} to ${newTier}?`,
      onOk: () => onDowngradeTier?.({
        userId: user.id,
        newTier: newTier as RegisteredUserTier,
      }),
    });
  };

  const handleExtendSubscription = (user: RegisteredUser) => {
    Modal.confirm({
      title: 'Extend Subscription',
      content: `Extend ${user.username}'s subscription by 30 days?`,
      onOk: () => onExtendSubscription?.({
        userId: user.id,
        extensionDays: 30,
      }),
    });
  };

  const handleFilterChange = (key: keyof RegisteredUserFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange?.(newFilters);
  };

  // ========================================================================
  // ROW SELECTION
  // ========================================================================

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: RegisteredUser) => ({
      disabled: false,
      name: record.username,
    }),
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder="Search registered users..."
                allowClear
                style={{ width: 300 }}
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                onSearch={(value) => handleFilterChange('search', value)}
              />
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowFilters(!showFilters)}
              >
                Filters
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                Refresh
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
              >
                Export
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                Add User
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <Row gutter={16}>
              <Col span={6}>
                <Select
                  placeholder="Select Tier"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.tier}
                  onChange={(value) => handleFilterChange('tier', value)}
                >
                  <Option value="free">Free</Option>
                  <Option value="premium">Premium</Option>
                  <Option value="enterprise">Enterprise</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="Select Status"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.isActive}
                  onChange={(value) => handleFilterChange('isActive', value)}
                >
                  <Option value={true}>Active</Option>
                  <Option value={false}>Inactive</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="Email Verification"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.isEmailVerified}
                  onChange={(value) => handleFilterChange('isEmailVerified', value)}
                >
                  <Option value={true}>Verified</Option>
                  <Option value={false}>Unverified</Option>
                </Select>
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="Created From"
                  style={{ width: '100%' }}
                  onChange={(date) => handleFilterChange('createdFrom', date?.toISOString())}
                />
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* Data Table */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={users}
          loading={loading}
          pagination={pagination ? {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`,
            onChange: pagination.onChange,
          } : false}
          rowKey="id"
          scroll={{ x: 1400 }}
          size="middle"
        />
      </Card>
    </div>
  );
}

export default RegisteredUserTable;
