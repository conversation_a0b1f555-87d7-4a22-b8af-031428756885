// APISportsGame CMS - Users API
// API client cho User Management theo CMS_DEVELOPMENT_GUIDE.md

import { apiClient } from '@/lib/api';
import {
  RegisteredUser,
  CreateRegisteredUserForm,
  UpdateRegisteredUserForm,
  RegisteredUserFilters,
  RegisteredUsersResponse,
  UserApiUsage,
  TierStatistics,
  UserSubscription,
  TierUpgradeRequest,
  TierDowngradeRequest,
  ExtendSubscriptionRequest,
} from '../types';

// ============================================================================
// API ENDPOINTS - Theo CMS_DEVELOPMENT_GUIDE.md
// ============================================================================

const USER_ENDPOINTS = {
  // RegisteredUser endpoints
  register: '/users/register',
  login: '/users/login',
  verifyEmail: '/users/verify-email',
  resendVerification: '/users/resend-verification',
  forgotPassword: '/users/forgot-password',
  resetPassword: '/users/reset-password',
  profile: '/users/profile',
  changePassword: '/users/change-password',
  apiUsage: '/users/api-usage',
  
  // Admin endpoints
  adminUsers: '/admin/users',
  tierStatistics: '/admin/tiers/statistics',
  approachingLimits: '/admin/users/approaching-limits',
  upgradeTier: (userId: number) => `/admin/users/${userId}/upgrade-tier`,
  downgradeTier: (userId: number) => `/admin/users/${userId}/downgrade-tier`,
  extendSubscription: (userId: number) => `/admin/users/${userId}/extend-subscription`,
  resetApiUsage: '/admin/reset-api-usage',
  checkUsageWarnings: '/admin/check-usage-warnings',
  userSubscription: (userId: number) => `/admin/users/${userId}/subscription`,
} as const;

// ============================================================================
// USER API CLASS
// ============================================================================

export class UserApi {
  // ========================================================================
  // REGISTERED USER OPERATIONS
  // ========================================================================

  /**
   * Get all registered users (Admin only)
   */
  async getUsers(filters?: RegisteredUserFilters): Promise<RegisteredUsersResponse> {
    const response = await apiClient.get<RegisteredUsersResponse>(
      USER_ENDPOINTS.adminUsers,
      { params: filters }
    );
    return response.data;
  }

  /**
   * Get user by ID
   */
  async getUser(userId: number): Promise<RegisteredUser> {
    const response = await apiClient.get<RegisteredUser>(
      `${USER_ENDPOINTS.adminUsers}/${userId}`
    );
    return response.data;
  }

  /**
   * Create new registered user (Admin only)
   */
  async createUser(userData: CreateRegisteredUserForm): Promise<RegisteredUser> {
    const response = await apiClient.post<RegisteredUser>(
      USER_ENDPOINTS.register,
      userData
    );
    return response.data;
  }

  /**
   * Update registered user (Admin only)
   */
  async updateUser(userId: number, userData: UpdateRegisteredUserForm): Promise<RegisteredUser> {
    const response = await apiClient.put<RegisteredUser>(
      `${USER_ENDPOINTS.adminUsers}/${userId}`,
      userData
    );
    return response.data;
  }

  /**
   * Delete registered user (Admin only)
   */
  async deleteUser(userId: number): Promise<void> {
    await apiClient.delete(`${USER_ENDPOINTS.adminUsers}/${userId}`);
  }

  // ========================================================================
  // ADMIN TIER MANAGEMENT
  // ========================================================================

  /**
   * Get tier statistics
   */
  async getTierStatistics(): Promise<TierStatistics[]> {
    const response = await apiClient.get<TierStatistics[]>(
      USER_ENDPOINTS.tierStatistics
    );
    return response.data;
  }

  /**
   * Upgrade user tier
   */
  async upgradeTier(data: TierUpgradeRequest): Promise<void> {
    await apiClient.post(
      USER_ENDPOINTS.upgradeTier(data.userId),
      {
        newTier: data.newTier,
        subscriptionEndDate: data.subscriptionEndDate,
      }
    );
  }

  /**
   * Downgrade user tier
   */
  async downgradeTier(data: TierDowngradeRequest): Promise<void> {
    await apiClient.post(
      USER_ENDPOINTS.downgradeTier(data.userId),
      {
        newTier: data.newTier,
        reason: data.reason,
      }
    );
  }

  /**
   * Extend user subscription
   */
  async extendSubscription(data: ExtendSubscriptionRequest): Promise<void> {
    await apiClient.post(
      USER_ENDPOINTS.extendSubscription(data.userId),
      {
        extensionDays: data.extensionDays,
        reason: data.reason,
      }
    );
  }

  /**
   * Get user subscription info
   */
  async getUserSubscription(userId: number): Promise<UserSubscription> {
    const response = await apiClient.get<UserSubscription>(
      USER_ENDPOINTS.userSubscription(userId)
    );
    return response.data;
  }

  // ========================================================================
  // API USAGE MANAGEMENT
  // ========================================================================

  /**
   * Get users approaching API limits
   */
  async getUsersApproachingLimits(): Promise<UserApiUsage[]> {
    const response = await apiClient.get<UserApiUsage[]>(
      USER_ENDPOINTS.approachingLimits
    );
    return response.data;
  }

  /**
   * Reset monthly API usage for all users
   */
  async resetApiUsage(): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.resetApiUsage);
  }

  /**
   * Check API usage warnings
   */
  async checkUsageWarnings(): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.checkUsageWarnings);
  }

  /**
   * Get user API usage statistics
   */
  async getUserApiUsage(userId: number): Promise<UserApiUsage> {
    const response = await apiClient.get<UserApiUsage>(
      `${USER_ENDPOINTS.apiUsage}/${userId}`
    );
    return response.data;
  }

  // ========================================================================
  // USER PROFILE OPERATIONS (For RegisteredUser self-management)
  // ========================================================================

  /**
   * Get current user profile
   */
  async getProfile(): Promise<RegisteredUser> {
    const response = await apiClient.get<RegisteredUser>(
      USER_ENDPOINTS.profile
    );
    return response.data;
  }

  /**
   * Update current user profile
   */
  async updateProfile(userData: Partial<UpdateRegisteredUserForm>): Promise<RegisteredUser> {
    const response = await apiClient.put<RegisteredUser>(
      USER_ENDPOINTS.profile,
      userData
    );
    return response.data;
  }

  /**
   * Change password
   */
  async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.changePassword, data);
  }

  // ========================================================================
  // EMAIL VERIFICATION
  // ========================================================================

  /**
   * Verify email with token
   */
  async verifyEmail(token: string): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.verifyEmail, { token });
  }

  /**
   * Resend verification email
   */
  async resendVerification(email: string): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.resendVerification, { email });
  }

  // ========================================================================
  // PASSWORD RESET
  // ========================================================================

  /**
   * Request password reset
   */
  async forgotPassword(email: string): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.forgotPassword, { email });
  }

  /**
   * Reset password with token
   */
  async resetPassword(data: {
    token: string;
    newPassword: string;
  }): Promise<void> {
    await apiClient.post(USER_ENDPOINTS.resetPassword, data);
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const userApi = new UserApi();
export default userApi;
