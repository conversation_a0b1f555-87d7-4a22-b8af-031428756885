// APISportsGame CMS - Users Hooks
// React hooks cho User Management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { userApi } from '../api';
import {
  RegisteredUser,
  CreateRegisteredUserForm,
  UpdateRegisteredUserForm,
  RegisteredUserFilters,
  UseUsersOptions,
  UseUsersReturn,
  UseUserMutationsReturn,
  TierUpgradeRequest,
  TierDowngradeRequest,
  ExtendSubscriptionRequest,
  UserApiUsage,
  TierStatistics,
} from '../types';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const USER_QUERY_KEYS = {
  all: ['users'] as const,
  lists: () => [...USER_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: RegisteredUserFilters) => [...USER_QUERY_KEYS.lists(), filters] as const,
  details: () => [...USER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_QUERY_KEYS.details(), id] as const,
  statistics: () => [...USER_QUERY_KEYS.all, 'statistics'] as const,
  tierStats: () => [...USER_QUERY_KEYS.statistics(), 'tiers'] as const,
  apiUsage: () => [...USER_QUERY_KEYS.all, 'api-usage'] as const,
  approachingLimits: () => [...USER_QUERY_KEYS.apiUsage(), 'approaching-limits'] as const,
  subscription: (userId: number) => [...USER_QUERY_KEYS.all, 'subscription', userId] as const,
} as const;

// ============================================================================
// USERS QUERY HOOK
// ============================================================================

export function useUsers(options: UseUsersOptions = {}): UseUsersReturn {
  const { filters, pagination, enabled = true } = options;

  const {
    data,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: USER_QUERY_KEYS.list(filters),
    queryFn: () => userApi.getUsers(filters),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    users: data?.data || [],
    loading: isLoading,
    error: error?.message || null,
    pagination: {
      current: data?.meta?.page || 1,
      pageSize: data?.meta?.limit || 10,
      total: data?.meta?.total || 0,
      totalPages: data?.meta?.totalPages || 0,
    },
    refetch,
  };
}

// ============================================================================
// USER DETAIL HOOK
// ============================================================================

export function useUser(userId: number, enabled = true) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.detail(userId),
    queryFn: () => userApi.getUser(userId),
    enabled: enabled && !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

// ============================================================================
// TIER STATISTICS HOOK
// ============================================================================

export function useTierStatistics() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.tierStats(),
    queryFn: () => userApi.getTierStatistics(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// ============================================================================
// USERS APPROACHING LIMITS HOOK
// ============================================================================

export function useUsersApproachingLimits() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.approachingLimits(),
    queryFn: () => userApi.getUsersApproachingLimits(),
    staleTime: 5 * 60 * 1000,
  });
}

// ============================================================================
// USER SUBSCRIPTION HOOK
// ============================================================================

export function useUserSubscription(userId: number, enabled = true) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.subscription(userId),
    queryFn: () => userApi.getUserSubscription(userId),
    enabled: enabled && !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

// ============================================================================
// USER MUTATIONS HOOK
// ============================================================================

export function useUserMutations(): UseUserMutationsReturn {
  const queryClient = useQueryClient();

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: (data: CreateRegisteredUserForm) => userApi.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.tierStats() });
      message.success('Tạo người dùng thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Tạo người dùng thất bại');
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateRegisteredUserForm }) =>
      userApi.updateUser(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.tierStats() });
      message.success('Cập nhật người dùng thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Cập nhật người dùng thất bại');
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: (id: number) => userApi.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.tierStats() });
      message.success('Xóa người dùng thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Xóa người dùng thất bại');
    },
  });

  // Upgrade tier mutation
  const upgradeTierMutation = useMutation({
    mutationFn: (data: TierUpgradeRequest) => userApi.upgradeTier(data),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(userId) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.tierStats() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.subscription(userId) });
      message.success('Nâng cấp gói thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Nâng cấp gói thất bại');
    },
  });

  // Downgrade tier mutation
  const downgradeTierMutation = useMutation({
    mutationFn: (data: TierDowngradeRequest) => userApi.downgradeTier(data),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(userId) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.tierStats() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.subscription(userId) });
      message.success('Hạ cấp gói thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Hạ cấp gói thất bại');
    },
  });

  // Extend subscription mutation
  const extendSubscriptionMutation = useMutation({
    mutationFn: (data: ExtendSubscriptionRequest) => userApi.extendSubscription(data),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(userId) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.subscription(userId) });
      message.success('Gia hạn subscription thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Gia hạn subscription thất bại');
    },
  });

  // Reset API usage mutation
  const resetApiUsageMutation = useMutation({
    mutationFn: () => userApi.resetApiUsage(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.approachingLimits() });
      message.success('Reset API usage thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Reset API usage thất bại');
    },
  });

  // Check usage warnings mutation
  const checkUsageWarningsMutation = useMutation({
    mutationFn: () => userApi.checkUsageWarnings(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.approachingLimits() });
      message.success('Kiểm tra cảnh báo thành công');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Kiểm tra cảnh báo thất bại');
    },
  });

  return {
    createUser: {
      mutate: createUserMutation.mutateAsync,
      loading: createUserMutation.isPending,
      error: createUserMutation.error?.message || null,
    },
    updateUser: {
      mutate: (id: number, data: UpdateRegisteredUserForm) =>
        updateUserMutation.mutateAsync({ id, data }),
      loading: updateUserMutation.isPending,
      error: updateUserMutation.error?.message || null,
    },
    deleteUser: {
      mutate: deleteUserMutation.mutateAsync,
      loading: deleteUserMutation.isPending,
      error: deleteUserMutation.error?.message || null,
    },
    upgradeTier: {
      mutate: upgradeTierMutation.mutateAsync,
      loading: upgradeTierMutation.isPending,
      error: upgradeTierMutation.error?.message || null,
    },
    downgradeTier: {
      mutate: downgradeTierMutation.mutateAsync,
      loading: downgradeTierMutation.isPending,
      error: downgradeTierMutation.error?.message || null,
    },
    extendSubscription: {
      mutate: extendSubscriptionMutation.mutateAsync,
      loading: extendSubscriptionMutation.isPending,
      error: extendSubscriptionMutation.error?.message || null,
    },
    resetApiUsage: {
      mutate: resetApiUsageMutation.mutateAsync,
      loading: resetApiUsageMutation.isPending,
      error: resetApiUsageMutation.error?.message || null,
    },
    checkUsageWarnings: {
      mutate: checkUsageWarningsMutation.mutateAsync,
      loading: checkUsageWarningsMutation.isPending,
      error: checkUsageWarningsMutation.error?.message || null,
    },
  };
}
