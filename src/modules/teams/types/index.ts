// APISportsGame CMS - Teams Module Types
// Team related types theo CMS_DEVELOPMENT_GUIDE.md

import { BaseEntity, BaseFilters } from '@/shared/types/common';

// ============================================================================
// TEAM ENTITY TYPES
// ============================================================================

// Team interface từ CMS_DEVELOPMENT_GUIDE.md
export interface Team extends BaseEntity {
  apiFootballId: number;
  name: string;
  code: string;
  country: string;
  founded: number;
  logo: string;
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateTeamForm {
  apiFootballId: number;
  name: string;
  code: string;
  country: string;
  founded: number;
  logo?: string;
}

export interface UpdateTeamForm {
  apiFootballId?: number;
  name?: string;
  code?: string;
  country?: string;
  founded?: number;
  logo?: string;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface TeamFilters extends BaseFilters {
  country?: string;
  foundedYear?: number;
  hasLogo?: boolean;
}

// ============================================================================
// API TYPES
// ============================================================================

export interface TeamApiResponse {
  data: Team[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface SingleTeamApiResponse {
  data: Team;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface TeamTableProps {
  teams: Team[];
  loading?: boolean;
  onEdit?: (team: Team) => void;
  onDelete?: (team: Team) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onSync?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: TeamFilters;
  onFiltersChange?: (filters: TeamFilters) => void;
}

export interface TeamFormProps {
  team?: Team;
  onSubmit: (data: CreateTeamForm | UpdateTeamForm) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export interface TeamCardProps {
  team: Team;
  onEdit?: (team: Team) => void;
  onDelete?: (team: Team) => void;
  onView?: (team: Team) => void;
  showActions?: boolean;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseTeamsOptions {
  filters?: TeamFilters;
  page?: number;
  limit?: number;
}

export interface UseTeamsReturn {
  teams: Team[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  refetch: () => void;
}

export interface UseTeamMutationsReturn {
  createTeam: {
    mutate: (data: CreateTeamForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  updateTeam: {
    mutate: (id: number, data: UpdateTeamForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  deleteTeam: {
    mutate: (id: number) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  syncTeams: {
    mutate: () => Promise<void>;
    loading: boolean;
    error: string | null;
  };
}

// ============================================================================
// SYNC TYPES
// ============================================================================

export interface TeamSyncStatus {
  isRunning: boolean;
  lastSync: Date | null;
  nextScheduled: Date | null;
  totalSynced: number;
  errors: string[];
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
}

export interface TeamSyncOptions {
  countries?: string[];
  leagues?: number[];
  forceUpdate?: boolean;
  includeInactive?: boolean;
}

// ============================================================================
// EXPORT ALL
// ============================================================================

export type {
  Team,
  CreateTeamForm,
  UpdateTeamForm,
  TeamFilters,
  TeamApiResponse,
  SingleTeamApiResponse,
  TeamTableProps,
  TeamFormProps,
  TeamCardProps,
  UseTeamsOptions,
  UseTeamsReturn,
  UseTeamMutationsReturn,
  TeamSyncStatus,
  TeamSyncOptions,
};
