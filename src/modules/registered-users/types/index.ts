// APISportsGame CMS - Registered Users Module Types
// RegisteredUser management types based on latest API documentation

import { BaseEntity, BaseFilters } from '@/shared/types/common';

// ============================================================================
// REGISTERED USER ENTITY TYPES
// ============================================================================

export interface RegisteredUser extends BaseEntity {
  username: string;
  email: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  tier: RegisteredUserTier;
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  apiCallsRemaining: number | null;
  hasActiveSubscription: boolean;
  subscriptionEndDate: Date | null;
  lastLoginAt: Date | null;
}

export type RegisteredUserTier = 'free' | 'premium' | 'enterprise';

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateRegisteredUserForm {
  username: string;
  email: string;
  password: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  tier?: RegisteredUserTier;
}

export interface UpdateRegisteredUserForm {
  fullName?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  tier?: RegisteredUserTier;
  isActive?: boolean;
  isEmailVerified?: boolean;
  apiCallsLimit?: number | null;
  subscriptionEndDate?: Date | null;
}

export interface ChangePasswordForm {
  currentPassword: string;
  newPassword: string;
}

// ============================================================================
// AUTH TYPES
// ============================================================================

export interface RegisteredUserLoginRequest {
  usernameOrEmail: string;
  password: string;
}

export interface RegisteredUserAuthResponse {
  accessToken: string;
  refreshToken: string;
  user: RegisteredUser;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface RegisteredUserFilters extends BaseFilters {
  tier?: RegisteredUserTier;
  isActive?: boolean;
  isEmailVerified?: boolean;
  hasActiveSubscription?: boolean;
  subscriptionStatus?: 'active' | 'expired' | 'expiring' | 'none';
  lastLoginFrom?: string;
  lastLoginTo?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface RegisteredUsersResponse {
  data: RegisteredUser[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface SingleRegisteredUserResponse {
  data: RegisteredUser;
}

// ============================================================================
// ADMIN MANAGEMENT TYPES
// ============================================================================

export interface TierUpgradeRequest {
  userId: number;
  newTier: RegisteredUserTier;
  subscriptionMonths?: number;
}

export interface TierDowngradeRequest {
  userId: number;
  newTier: RegisteredUserTier;
}

export interface ExtendSubscriptionRequest {
  userId: number;
  additionalMonths: number;
}

export interface UserApiUsage {
  tier: RegisteredUserTier;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  apiCallsRemaining: number | null;
  lastApiCallAt: Date | null;
  resetDate: Date;
}

export interface TierStatistics {
  free: number;
  premium: number;
  enterprise: number;
  total: number;
}

export interface UserSubscription {
  userId: number;
  tier: RegisteredUserTier;
  isActive: boolean;
  startDate: Date;
  endDate: Date | null;
  autoRenew: boolean;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface RegisteredUserTableProps {
  users: RegisteredUser[];
  loading?: boolean;
  onEdit?: (user: RegisteredUser) => void;
  onDelete?: (user: RegisteredUser) => void;
  onUpgradeTier?: (data: TierUpgradeRequest) => void;
  onDowngradeTier?: (data: TierDowngradeRequest) => void;
  onExtendSubscription?: (data: ExtendSubscriptionRequest) => void;
  onViewUsage?: (user: RegisteredUser) => void;
  onToggleStatus?: (user: RegisteredUser) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: RegisteredUserFilters;
  onFiltersChange?: (filters: RegisteredUserFilters) => void;
}

export interface RegisteredUserFormProps {
  user?: RegisteredUser;
  onSubmit: (data: CreateRegisteredUserForm | UpdateRegisteredUserForm) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export interface RegisteredUserCardProps {
  user: RegisteredUser;
  onEdit?: (user: RegisteredUser) => void;
  onDelete?: (user: RegisteredUser) => void;
  onView?: (user: RegisteredUser) => void;
  onUpgradeTier?: (user: RegisteredUser) => void;
  onDowngradeTier?: (user: RegisteredUser) => void;
  showActions?: boolean;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseRegisteredUsersOptions {
  filters?: RegisteredUserFilters;
  page?: number;
  limit?: number;
}

export interface UseRegisteredUsersReturn {
  users: RegisteredUser[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  refetch: () => void;
}

export interface UseRegisteredUserMutationsReturn {
  createUser: {
    mutate: (data: CreateRegisteredUserForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  updateUser: {
    mutate: (id: number, data: UpdateRegisteredUserForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  deleteUser: {
    mutate: (id: number) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  upgradeTier: {
    mutate: (data: TierUpgradeRequest) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  downgradeTier: {
    mutate: (data: TierDowngradeRequest) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  extendSubscription: {
    mutate: (data: ExtendSubscriptionRequest) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  resetApiUsage: {
    mutate: () => Promise<void>;
    loading: boolean;
    error: string | null;
  };
}

// ============================================================================
// STATISTICS TYPES
// ============================================================================

export interface RegisteredUserStatistics {
  total: number;
  active: number;
  verified: number;
  tierDistribution: TierStatistics;
  apiUsageStats: {
    totalCalls: number;
    averageCallsPerUser: number;
    usersNearLimit: number;
  };
  subscriptionStats: {
    activeSubscriptions: number;
    expiringSoon: number;
    expired: number;
  };
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export const isRegisteredUser = (user: any): user is RegisteredUser => {
  return user && typeof user === 'object' && 'tier' in user;
};

export const isFreeUser = (user: RegisteredUser): boolean => {
  return user.tier === 'free';
};

export const isPremiumUser = (user: RegisteredUser): boolean => {
  return user.tier === 'premium';
};

export const isEnterpriseUser = (user: RegisteredUser): boolean => {
  return user.tier === 'enterprise';
};

// ============================================================================
// CONSTANTS
// ============================================================================

export const REGISTERED_USER_TIERS: RegisteredUserTier[] = ['free', 'premium', 'enterprise'];

export const TIER_LABELS: Record<RegisteredUserTier, string> = {
  free: 'Free',
  premium: 'Premium',
  enterprise: 'Enterprise',
};

export const TIER_DESCRIPTIONS: Record<RegisteredUserTier, string> = {
  free: 'Basic features with limited API calls',
  premium: 'Enhanced features with increased API limits',
  enterprise: 'Full features with unlimited API access',
};

export const TIER_COLORS: Record<RegisteredUserTier, string> = {
  free: 'default',
  premium: 'gold',
  enterprise: 'purple',
};

export const TIER_LIMITS: Record<RegisteredUserTier, number | null> = {
  free: 100,
  premium: 10000,
  enterprise: null, // unlimited
};

// ============================================================================
// EXPORT ALL
// ============================================================================

export type {
  RegisteredUser,
  RegisteredUserTier,
  CreateRegisteredUserForm,
  UpdateRegisteredUserForm,
  ChangePasswordForm,
  RegisteredUserLoginRequest,
  RegisteredUserAuthResponse,
  RegisteredUserFilters,
  RegisteredUsersResponse,
  SingleRegisteredUserResponse,
  TierUpgradeRequest,
  TierDowngradeRequest,
  ExtendSubscriptionRequest,
  UserApiUsage,
  TierStatistics,
  UserSubscription,
  RegisteredUserTableProps,
  RegisteredUserFormProps,
  RegisteredUserCardProps,
  UseRegisteredUsersOptions,
  UseRegisteredUsersReturn,
  UseRegisteredUserMutationsReturn,
  RegisteredUserStatistics,
};
