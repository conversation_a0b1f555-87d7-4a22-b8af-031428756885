// APISportsGame CMS - SystemUser Table Component
// Table component for SystemUser management with admin operations

'use client';

import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Dropdown,
  Modal,
  Input,
  Select,
  Card,
  Row,
  Col,
  Avatar,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  ExportOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CrownOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import {
  SystemUser,
  SystemUserFilters,
  SystemRole,
  SystemUserTableProps,
} from '../types';
import { formatDate } from '@/shared/utils/format';

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// ============================================================================
// SYSTEM USER TABLE COMPONENT
// ============================================================================

export function SystemUserTable({
  users,
  loading = false,
  onEdit,
  onDelete,
  onToggleStatus,
  onResetPassword,
  onAdd,
  onRefresh,
  onExport,
  pagination,
  filters = {},
  onFiltersChange,
}: SystemUserTableProps) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // ========================================================================
  // HELPER FUNCTIONS
  // ========================================================================

  const getRoleColor = (role: SystemRole) => {
    const colors = {
      admin: 'red',
      editor: 'blue',
      moderator: 'green',
    };
    return colors[role];
  };

  const getRoleIcon = (role: SystemRole) => {
    const icons = {
      admin: '👑',
      editor: '✏️',
      moderator: '🛡️',
    };
    return icons[role];
  };

  // ========================================================================
  // COLUMN DEFINITIONS
  // ========================================================================

  const columns: ColumnsType<SystemUser> = [
    {
      title: 'User',
      dataIndex: 'username',
      key: 'username',
      render: (username: string, record: SystemUser) => (
        <Space>
          <Avatar 
            size="small" 
            icon={<UserOutlined />} 
            style={{ backgroundColor: getRoleColor(record.role) }}
          />
          <div>
            <div className="font-medium">{username}</div>
            <Text type="secondary" className="text-xs">
              {record.email}
            </Text>
          </div>
        </Space>
      ),
      sorter: true,
    },
    {
      title: 'Full Name',
      dataIndex: 'fullName',
      key: 'fullName',
      render: (fullName: string) => (
        <Text>{fullName || <Text type="secondary">Not provided</Text>}</Text>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: SystemRole) => (
        <Tag color={getRoleColor(role)} icon={getRoleIcon(role)}>
          {role.toUpperCase()}
        </Tag>
      ),
      filters: [
        { text: 'Admin', value: 'admin' },
        { text: 'Editor', value: 'editor' },
        { text: 'Moderator', value: 'moderator' },
      ],
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag 
          color={isActive ? 'success' : 'error'}
          icon={isActive ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
        >
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      render: (date: Date | null) => (
        <Text type="secondary" className="text-sm">
          {date ? formatDate(date) : 'Never'}
        </Text>
      ),
      sorter: true,
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: Date) => (
        <Text type="secondary" className="text-sm">
          {formatDate(date)}
        </Text>
      ),
      sorter: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: SystemUser) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewUser(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: 'Edit User',
                  icon: <EditOutlined />,
                  onClick: () => onEdit?.(record),
                },
                {
                  key: 'toggle-status',
                  label: record.isActive ? 'Deactivate' : 'Activate',
                  icon: record.isActive ? <CloseCircleOutlined /> : <CheckCircleOutlined />,
                  onClick: () => onToggleStatus?.(record),
                },
                {
                  key: 'reset-password',
                  label: 'Reset Password',
                  icon: <CrownOutlined />,
                  onClick: () => handleResetPassword(record),
                },
                {
                  type: 'divider',
                },
                {
                  key: 'delete',
                  label: 'Delete User',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
            trigger={['click']}
          >
            <Button type="text" size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleDelete = (user: SystemUser) => {
    Modal.confirm({
      title: 'Delete System User',
      content: `Are you sure you want to delete user "${user.username}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => onDelete?.(user),
    });
  };

  const handleResetPassword = (user: SystemUser) => {
    Modal.confirm({
      title: 'Reset Password',
      content: `Reset password for user "${user.username}"? They will need to set a new password.`,
      okText: 'Reset',
      okType: 'primary',
      onOk: () => onResetPassword?.(user),
    });
  };

  const handleViewUser = (user: SystemUser) => {
    Modal.info({
      title: `System User Details - ${user.username}`,
      content: (
        <div className="space-y-4">
          <div>
            <strong>Username:</strong> {user.username}
          </div>
          <div>
            <strong>Email:</strong> {user.email}
          </div>
          <div>
            <strong>Full Name:</strong> {user.fullName || 'Not provided'}
          </div>
          <div>
            <strong>Role:</strong> 
            <Tag color={getRoleColor(user.role)} className="ml-2">
              {getRoleIcon(user.role)} {user.role.toUpperCase()}
            </Tag>
          </div>
          <div>
            <strong>Status:</strong>
            <Tag 
              color={user.isActive ? 'success' : 'error'} 
              className="ml-2"
            >
              {user.isActive ? 'Active' : 'Inactive'}
            </Tag>
          </div>
          <div>
            <strong>Last Login:</strong> {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
          </div>
          <div>
            <strong>Created:</strong> {formatDate(user.createdAt)}
          </div>
          <div>
            <strong>Updated:</strong> {formatDate(user.updatedAt)}
          </div>
        </div>
      ),
      width: 500,
    });
  };

  const handleFilterChange = (key: keyof SystemUserFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange?.(newFilters);
  };

  // ========================================================================
  // ROW SELECTION
  // ========================================================================

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: SystemUser) => ({
      disabled: false,
      name: record.username,
    }),
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder="Search system users..."
                allowClear
                style={{ width: 300 }}
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                onSearch={(value) => handleFilterChange('search', value)}
              />
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowFilters(!showFilters)}
              >
                Filters
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                Refresh
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
              >
                Export
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                Add System User
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <Row gutter={16}>
              <Col span={8}>
                <Select
                  placeholder="Select Role"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.role}
                  onChange={(value) => handleFilterChange('role', value)}
                >
                  <Option value="admin">Admin</Option>
                  <Option value="editor">Editor</Option>
                  <Option value="moderator">Moderator</Option>
                </Select>
              </Col>
              <Col span={8}>
                <Select
                  placeholder="Select Status"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.isActive}
                  onChange={(value) => handleFilterChange('isActive', value)}
                >
                  <Option value={true}>Active</Option>
                  <Option value={false}>Inactive</Option>
                </Select>
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* Data Table */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={users}
          loading={loading}
          pagination={pagination ? {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} system users`,
            onChange: pagination.onChange,
          } : false}
          rowKey="id"
          scroll={{ x: 1200 }}
          size="middle"
        />
      </Card>
    </div>
  );
}

export default SystemUserTable;
