// APISportsGame CMS - SystemUser Form Component
// Form component for creating/editing SystemUser

'use client';

import React, { useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Card,
  Row,
  Col,
  Alert,
  Switch,
  Typography,
} from 'antd';
import {
  UserOutlined,
  <PERSON>Outlined,
  LockOutlined,
  IdcardOutlined,
  CrownOutlined,
  SaveOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import {
  SystemUser,
  CreateSystemUserForm,
  UpdateSystemUserForm,
  SystemUserFormProps,
  SystemRole,
  SYSTEM_ROLES,
  ROLE_LABELS,
  ROLE_DESCRIPTIONS,
} from '../types';

const { Option } = Select;
const { Title, Text } = Typography;

// ============================================================================
// SYSTEM USER FORM COMPONENT
// ============================================================================

export function SystemUserForm({
  user,
  onSubmit,
  onCancel,
  loading = false,
  mode,
}: SystemUserFormProps) {
  const [form] = Form.useForm();

  // ========================================================================
  // EFFECTS
  // ========================================================================

  useEffect(() => {
    if (mode === 'edit' && user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        fullName: user.fullName || '',
        role: user.role,
        isActive: user.isActive,
      });
    } else {
      form.resetFields();
    }
  }, [user, mode, form]);

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleSubmit = async (values: any) => {
    try {
      if (mode === 'create') {
        const createData: CreateSystemUserForm = {
          username: values.username,
          email: values.email,
          password: values.password,
          role: values.role,
          fullName: values.fullName,
        };
        await onSubmit(createData);
      } else {
        const updateData: UpdateSystemUserForm = {
          email: values.email,
          fullName: values.fullName,
          role: values.role,
          isActive: values.isActive,
        };
        await onSubmit(updateData);
      }
    } catch (error) {
      console.error('Form submit error:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // ========================================================================
  // VALIDATION RULES
  // ========================================================================

  const validationRules = {
    username: [
      { required: true, message: 'Username is required' },
      { min: 3, message: 'Username must be at least 3 characters' },
      { max: 50, message: 'Username must not exceed 50 characters' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: 'Username can only contain letters, numbers, and underscores' },
    ],
    email: [
      { required: true, message: 'Email is required' },
      { type: 'email' as const, message: 'Please enter a valid email address' },
    ],
    password: [
      { required: mode === 'create', message: 'Password is required' },
      { min: 8, message: 'Password must be at least 8 characters' },
      { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number' },
    ],
    confirmPassword: [
      { required: mode === 'create', message: 'Please confirm your password' },
      ({ getFieldValue }: any) => ({
        validator(_: any, value: string) {
          if (!value || getFieldValue('password') === value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error('Passwords do not match'));
        },
      }),
    ],
    role: [
      { required: true, message: 'Role is required' },
    ],
    fullName: [
      { max: 100, message: 'Full name must not exceed 100 characters' },
    ],
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-6">
      {/* Form Header */}
      <div>
        <Title level={4} className="mb-2">
          {mode === 'create' ? 'Create System User' : 'Edit System User'}
        </Title>
        <Text type="secondary">
          {mode === 'create' 
            ? 'Create a new system user with appropriate role and permissions'
            : 'Update system user information and permissions'
          }
        </Text>
      </div>

      {/* Security Notice */}
      {mode === 'create' && (
        <Alert
          message="Security Notice"
          description="System users have administrative access. Only create accounts for trusted personnel."
          type="warning"
          showIcon
        />
      )}

      {/* Form */}
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="large"
          disabled={loading}
        >
          <Row gutter={24}>
            {/* Basic Information */}
            <Col span={24}>
              <Title level={5} className="mb-4">Basic Information</Title>
            </Col>

            <Col span={12}>
              <Form.Item
                name="username"
                label="Username"
                rules={validationRules.username}
              >
                <Input 
                  prefix={<UserOutlined />} 
                  placeholder="Enter username"
                  disabled={mode === 'edit'} // Username cannot be changed
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="email"
                label="Email Address"
                rules={validationRules.email}
              >
                <Input 
                  prefix={<MailOutlined />} 
                  placeholder="Enter email address"
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                name="fullName"
                label="Full Name"
                rules={validationRules.fullName}
              >
                <Input 
                  prefix={<IdcardOutlined />} 
                  placeholder="Enter full name (optional)"
                />
              </Form.Item>
            </Col>

            {/* Password Section (Create only) */}
            {mode === 'create' && (
              <>
                <Col span={24}>
                  <Title level={5} className="mb-4 mt-6">Security</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="password"
                    label="Password"
                    rules={validationRules.password}
                  >
                    <Input.Password 
                      prefix={<LockOutlined />} 
                      placeholder="Enter password"
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="confirmPassword"
                    label="Confirm Password"
                    rules={validationRules.confirmPassword}
                  >
                    <Input.Password 
                      prefix={<LockOutlined />} 
                      placeholder="Confirm password"
                    />
                  </Form.Item>
                </Col>
              </>
            )}

            {/* Role & Permissions */}
            <Col span={24}>
              <Title level={5} className="mb-4 mt-6">Role & Permissions</Title>
            </Col>

            <Col span={12}>
              <Form.Item
                name="role"
                label="Role"
                rules={validationRules.role}
              >
                <Select 
                  placeholder="Select role"
                  suffixIcon={<CrownOutlined />}
                >
                  {SYSTEM_ROLES.map((role) => (
                    <Option key={role} value={role}>
                      <div>
                        <div className="font-medium">{ROLE_LABELS[role]}</div>
                        <div className="text-xs text-gray-500">
                          {ROLE_DESCRIPTIONS[role]}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* Status (Edit only) */}
            {mode === 'edit' && (
              <Col span={12}>
                <Form.Item
                  name="isActive"
                  label="Account Status"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="Active" 
                    unCheckedChildren="Inactive"
                  />
                </Form.Item>
              </Col>
            )}

            {/* Form Actions */}
            <Col span={24}>
              <Form.Item className="mb-0 mt-6">
                <Space className="w-full justify-end">
                  <Button
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    <CloseOutlined /> Cancel
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                  >
                    <SaveOutlined /> {mode === 'create' ? 'Create User' : 'Update User'}
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Role Information */}
      <Card title="Role Permissions" size="small">
        <div className="space-y-3">
          <div className="flex justify-between items-center p-3 bg-red-50 rounded">
            <div>
              <div className="font-medium text-red-800">👑 Administrator</div>
              <div className="text-sm text-red-600">Full system access and user management</div>
            </div>
          </div>
          <div className="flex justify-between items-center p-3 bg-blue-50 rounded">
            <div>
              <div className="font-medium text-blue-800">✏️ Editor</div>
              <div className="text-sm text-blue-600">Content management and data operations</div>
            </div>
          </div>
          <div className="flex justify-between items-center p-3 bg-green-50 rounded">
            <div>
              <div className="font-medium text-green-800">🛡️ Moderator</div>
              <div className="text-sm text-green-600">Content moderation and basic operations</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default SystemUserForm;
