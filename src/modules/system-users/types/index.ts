// APISportsGame CMS - System Users Module Types
// SystemUser management types based on latest API documentation

import { BaseEntity, BaseFilters } from '@/shared/types/common';

// ============================================================================
// SYSTEM USER ENTITY TYPES
// ============================================================================

export interface SystemUser extends BaseEntity {
  username: string;
  email: string;
  fullName?: string;
  role: SystemRole;
  isActive: boolean;
  lastLoginAt: Date | null;
}

export type SystemRole = 'admin' | 'editor' | 'moderator';

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateSystemUserForm {
  username: string;
  email: string;
  password: string;
  role: SystemRole;
  fullName?: string;
}

export interface UpdateSystemUserForm {
  email?: string;
  fullName?: string;
  role?: SystemRole;
  isActive?: boolean;
}

export interface ChangePasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// ============================================================================
// AUTH TYPES
// ============================================================================

export interface SystemUserLoginRequest {
  username: string;
  password: string;
}

export interface SystemAuthResponse {
  accessToken: string;
  refreshToken: string;
  user: SystemUser;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface SystemUserFilters extends BaseFilters {
  role?: SystemRole;
  isActive?: boolean;
  lastLoginFrom?: string;
  lastLoginTo?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface SystemUsersResponse {
  data: SystemUser[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface SingleSystemUserResponse {
  data: SystemUser;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface SystemUserTableProps {
  users: SystemUser[];
  loading?: boolean;
  onEdit?: (user: SystemUser) => void;
  onDelete?: (user: SystemUser) => void;
  onToggleStatus?: (user: SystemUser) => void;
  onResetPassword?: (user: SystemUser) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: SystemUserFilters;
  onFiltersChange?: (filters: SystemUserFilters) => void;
}

export interface SystemUserFormProps {
  user?: SystemUser;
  onSubmit: (data: CreateSystemUserForm | UpdateSystemUserForm) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export interface SystemUserCardProps {
  user: SystemUser;
  onEdit?: (user: SystemUser) => void;
  onDelete?: (user: SystemUser) => void;
  onView?: (user: SystemUser) => void;
  onToggleStatus?: (user: SystemUser) => void;
  showActions?: boolean;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseSystemUsersOptions {
  filters?: SystemUserFilters;
  page?: number;
  limit?: number;
}

export interface UseSystemUsersReturn {
  users: SystemUser[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  refetch: () => void;
}

export interface UseSystemUserMutationsReturn {
  createUser: {
    mutate: (data: CreateSystemUserForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  updateUser: {
    mutate: (id: number, data: UpdateSystemUserForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  deleteUser: {
    mutate: (id: number) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  changePassword: {
    mutate: (data: ChangePasswordForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  logout: {
    mutate: (refreshToken: string) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  logoutAllDevices: {
    mutate: () => Promise<void>;
    loading: boolean;
    error: string | null;
  };
}

// ============================================================================
// PROFILE TYPES
// ============================================================================

export interface SystemUserProfile extends SystemUser {
  // Additional profile-specific fields if needed
}

export interface UpdateProfileForm {
  email?: string;
  fullName?: string;
}

// ============================================================================
// STATISTICS TYPES
// ============================================================================

export interface SystemUserStatistics {
  total: number;
  active: number;
  inactive: number;
  roleDistribution: {
    admin: number;
    editor: number;
    moderator: number;
  };
  recentLogins: number;
}

// ============================================================================
// PERMISSION TYPES
// ============================================================================

export interface SystemUserPermissions {
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canManageRoles: boolean;
  canViewAllUsers: boolean;
  canManageSystem: boolean;
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export const isSystemUser = (user: any): user is SystemUser => {
  return user && typeof user === 'object' && 'role' in user;
};

export const isAdminRole = (role: SystemRole): boolean => {
  return role === 'admin';
};

export const isEditorRole = (role: SystemRole): boolean => {
  return role === 'editor';
};

export const isModeratorRole = (role: SystemRole): boolean => {
  return role === 'moderator';
};

// ============================================================================
// CONSTANTS
// ============================================================================

export const SYSTEM_ROLES: SystemRole[] = ['admin', 'editor', 'moderator'];

export const ROLE_LABELS: Record<SystemRole, string> = {
  admin: 'Administrator',
  editor: 'Editor',
  moderator: 'Moderator',
};

export const ROLE_DESCRIPTIONS: Record<SystemRole, string> = {
  admin: 'Full system access and user management',
  editor: 'Content management and data operations',
  moderator: 'Content moderation and basic operations',
};

export const ROLE_COLORS: Record<SystemRole, string> = {
  admin: 'red',
  editor: 'blue',
  moderator: 'green',
};

// ============================================================================
// EXPORT ALL
// ============================================================================

export type {
  SystemUser,
  SystemRole,
  CreateSystemUserForm,
  UpdateSystemUserForm,
  ChangePasswordForm,
  SystemUserLoginRequest,
  SystemAuthResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  SystemUserFilters,
  SystemUsersResponse,
  SingleSystemUserResponse,
  SystemUserTableProps,
  SystemUserFormProps,
  SystemUserCardProps,
  UseSystemUsersOptions,
  UseSystemUsersReturn,
  UseSystemUserMutationsReturn,
  SystemUserProfile,
  UpdateProfileForm,
  SystemUserStatistics,
  SystemUserPermissions,
};
