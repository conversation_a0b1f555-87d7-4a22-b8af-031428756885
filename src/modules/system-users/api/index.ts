// APISportsGame CMS - System Users API
// API client for SystemUser management based on latest API documentation

import { apiClient } from '@/lib/api';
import {
  SystemUser,
  CreateSystemUserForm,
  UpdateSystemUserForm,
  SystemUserFilters,
  SystemUsersResponse,
  SystemAuthResponse,
  SystemUserLoginRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordForm,
} from '../types';

// ============================================================================
// API ENDPOINTS - Based on latest API documentation
// ============================================================================

const SYSTEM_AUTH_ENDPOINTS = {
  // Authentication
  login: '/system-auth/login',
  refresh: '/system-auth/refresh',
  logout: '/system-auth/logout',
  logoutAll: '/system-auth/logout-all',

  // Profile management
  profile: '/system-auth/profile',
  changePassword: '/system-auth/change-password',

  // User management (admin only)
  createUser: '/system-auth/create-user',
  updateUser: (id: number) => `/system-auth/users/${id}`,

  // Note: List users endpoint might be in admin section
  // Will use admin endpoints for user listing
  listUsers: '/admin/users', // This might need to be system-users specific
} as const;

// ============================================================================
// SYSTEM USER API CLASS
// ============================================================================

export class SystemUserApi {
  // ========================================================================
  // AUTHENTICATION OPERATIONS
  // ========================================================================

  /**
   * Login system user
   */
  async login(credentials: SystemUserLoginRequest): Promise<SystemAuthResponse> {
    const response = await apiClient.post<SystemAuthResponse>(
      SYSTEM_AUTH_ENDPOINTS.login,
      credentials
    );
    return response.data;
  }

  /**
   * Refresh access token
   */
  async refreshToken(data: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    const response = await apiClient.post<RefreshTokenResponse>(
      SYSTEM_AUTH_ENDPOINTS.refresh,
      data
    );
    return response.data;
  }

  /**
   * Logout current session
   */
  async logout(refreshToken: string): Promise<void> {
    await apiClient.post(SYSTEM_AUTH_ENDPOINTS.logout, { refreshToken });
  }

  /**
   * Logout from all devices
   */
  async logoutAllDevices(): Promise<void> {
    await apiClient.post(SYSTEM_AUTH_ENDPOINTS.logoutAll);
  }

  // ========================================================================
  // PROFILE OPERATIONS
  // ========================================================================

  /**
   * Get current user profile
   */
  async getProfile(): Promise<SystemUser> {
    const response = await apiClient.get<SystemUser>(
      SYSTEM_AUTH_ENDPOINTS.profile
    );
    return response.data;
  }

  /**
   * Update current user profile
   */
  async updateProfile(data: UpdateSystemUserForm): Promise<SystemUser> {
    const response = await apiClient.put<SystemUser>(
      SYSTEM_AUTH_ENDPOINTS.profile,
      data
    );
    return response.data;
  }

  /**
   * Change current user password
   */
  async changePassword(data: ChangePasswordForm): Promise<void> {
    await apiClient.post(SYSTEM_AUTH_ENDPOINTS.changePassword, {
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
      confirmPassword: data.confirmPassword,
    });
  }

  // ========================================================================
  // USER MANAGEMENT OPERATIONS (Admin only)
  // ========================================================================

  /**
   * Get all system users (Admin only)
   * Note: Need to implement system-users specific endpoint
   */
  async getUsers(filters?: SystemUserFilters): Promise<SystemUsersResponse> {
    // This will need to be implemented as a separate endpoint for system users
    // For now, we'll create a mock response structure
    const response = await apiClient.get<any>(
      '/admin/system-users', // This endpoint needs to be created
      { params: filters }
    );

    // Transform response to match our interface
    return {
      data: response.data?.data || [],
      meta: response.data?.meta || {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
    };
  }

  /**
   * Get system user by ID
   */
  async getUser(userId: number): Promise<SystemUser> {
    const response = await apiClient.get<SystemUser>(
      `${SYSTEM_AUTH_ENDPOINTS.listUsers}/${userId}`
    );
    return response.data;
  }

  /**
   * Create new system user (Admin only)
   */
  async createUser(userData: CreateSystemUserForm): Promise<SystemUser> {
    const response = await apiClient.post<SystemUser>(
      SYSTEM_AUTH_ENDPOINTS.createUser,
      userData
    );
    return response.data;
  }

  /**
   * Update system user by ID (Admin only)
   */
  async updateUser(userId: number, userData: UpdateSystemUserForm): Promise<SystemUser> {
    const response = await apiClient.put<SystemUser>(
      SYSTEM_AUTH_ENDPOINTS.updateUser(userId),
      userData
    );
    return response.data;
  }

  /**
   * Delete system user (Admin only)
   * Note: This endpoint might not exist, need to check API
   */
  async deleteUser(userId: number): Promise<void> {
    await apiClient.delete(`${SYSTEM_AUTH_ENDPOINTS.listUsers}/${userId}`);
  }

  /**
   * Toggle user active status (Admin only)
   */
  async toggleUserStatus(userId: number, isActive: boolean): Promise<SystemUser> {
    const response = await apiClient.put<SystemUser>(
      SYSTEM_AUTH_ENDPOINTS.updateUser(userId),
      { isActive }
    );
    return response.data;
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  /**
   * Check if user has permission for operation
   */
  hasPermission(currentUser: SystemUser, operation: string): boolean {
    if (!currentUser) return false;

    switch (operation) {
      case 'create_user':
      case 'delete_user':
      case 'manage_roles':
        return currentUser.role === 'admin';

      case 'edit_user':
      case 'view_users':
        return ['admin', 'editor'].includes(currentUser.role);

      case 'view_profile':
      case 'edit_profile':
        return true; // All system users can manage their own profile

      default:
        return false;
    }
  }

  /**
   * Get role permissions
   */
  getRolePermissions(role: SystemRole): string[] {
    const permissions = {
      admin: [
        'create_user',
        'edit_user',
        'delete_user',
        'view_users',
        'manage_roles',
        'manage_system',
        'view_analytics',
        'manage_data',
      ],
      editor: [
        'edit_user',
        'view_users',
        'manage_data',
        'view_analytics',
      ],
      moderator: [
        'view_users',
        'moderate_content',
      ],
    };

    return permissions[role] || [];
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const systemUserApi = new SystemUserApi();
export default systemUserApi;
