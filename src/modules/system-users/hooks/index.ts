// APISportsGame CMS - System Users Hooks
// React hooks for SystemUser management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { systemUserApi } from '../api';
import {
  SystemUser,
  CreateSystemUserForm,
  UpdateSystemUserForm,
  SystemUserFilters,
  UseSystemUsersOptions,
  UseSystemUsersReturn,
  UseSystemUserMutationsReturn,
  ChangePasswordForm,
} from '../types';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const SYSTEM_USER_QUERY_KEYS = {
  all: ['system-users'] as const,
  lists: () => [...SYSTEM_USER_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: SystemUserFilters) => [...SYSTEM_USER_QUERY_KEYS.lists(), filters] as const,
  details: () => [...SYSTEM_USER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...SYSTEM_USER_QUERY_KEYS.details(), id] as const,
  profile: () => [...SYSTEM_USER_QUERY_KEYS.all, 'profile'] as const,
  statistics: () => [...SYSTEM_USER_QUERY_KEYS.all, 'statistics'] as const,
} as const;

// ============================================================================
// SYSTEM USERS QUERY HOOK
// ============================================================================

export function useSystemUsers(options: UseSystemUsersOptions = {}): UseSystemUsersReturn {
  const { filters, page = 1, limit = 10 } = options;

  const {
    data,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: SYSTEM_USER_QUERY_KEYS.list({ ...filters, page, limit }),
    queryFn: () => systemUserApi.getUsers({ ...filters, page, limit }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    users: data?.data || [],
    loading: isLoading,
    error: error?.message || null,
    pagination: {
      current: data?.meta?.page || 1,
      pageSize: data?.meta?.limit || 10,
      total: data?.meta?.total || 0,
      totalPages: data?.meta?.totalPages || 0,
    },
    refetch,
  };
}

// ============================================================================
// SYSTEM USER DETAIL HOOK
// ============================================================================

export function useSystemUser(userId: number, enabled = true) {
  return useQuery({
    queryKey: SYSTEM_USER_QUERY_KEYS.detail(userId),
    queryFn: () => systemUserApi.getUser(userId),
    enabled: enabled && !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

// ============================================================================
// CURRENT USER PROFILE HOOK
// ============================================================================

export function useSystemUserProfile() {
  return useQuery({
    queryKey: SYSTEM_USER_QUERY_KEYS.profile(),
    queryFn: () => systemUserApi.getProfile(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// ============================================================================
// SYSTEM USER MUTATIONS HOOK
// ============================================================================

export function useSystemUserMutations(): UseSystemUserMutationsReturn {
  const queryClient = useQueryClient();

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: (data: CreateSystemUserForm) => systemUserApi.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SYSTEM_USER_QUERY_KEYS.lists() });
      message.success('System user created successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Failed to create system user');
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateSystemUserForm }) =>
      systemUserApi.updateUser(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: SYSTEM_USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: SYSTEM_USER_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: SYSTEM_USER_QUERY_KEYS.profile() });
      message.success('System user updated successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Failed to update system user');
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: (id: number) => systemUserApi.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SYSTEM_USER_QUERY_KEYS.lists() });
      message.success('System user deleted successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Failed to delete system user');
    },
  });

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: (data: ChangePasswordForm) => systemUserApi.changePassword(data),
    onSuccess: () => {
      message.success('Password changed successfully. Please login again.');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Failed to change password');
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: (refreshToken: string) => systemUserApi.logout(refreshToken),
    onSuccess: () => {
      queryClient.clear(); // Clear all cached data
      message.success('Logged out successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Logout failed');
    },
  });

  // Logout all devices mutation
  const logoutAllDevicesMutation = useMutation({
    mutationFn: () => systemUserApi.logoutAllDevices(),
    onSuccess: () => {
      queryClient.clear(); // Clear all cached data
      message.success('Logged out from all devices successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Failed to logout from all devices');
    },
  });

  return {
    createUser: {
      mutate: createUserMutation.mutateAsync,
      loading: createUserMutation.isPending,
      error: createUserMutation.error?.message || null,
    },
    updateUser: {
      mutate: (id: number, data: UpdateSystemUserForm) =>
        updateUserMutation.mutateAsync({ id, data }),
      loading: updateUserMutation.isPending,
      error: updateUserMutation.error?.message || null,
    },
    deleteUser: {
      mutate: deleteUserMutation.mutateAsync,
      loading: deleteUserMutation.isPending,
      error: deleteUserMutation.error?.message || null,
    },
    changePassword: {
      mutate: changePasswordMutation.mutateAsync,
      loading: changePasswordMutation.isPending,
      error: changePasswordMutation.error?.message || null,
    },
    logout: {
      mutate: logoutMutation.mutateAsync,
      loading: logoutMutation.isPending,
      error: logoutMutation.error?.message || null,
    },
    logoutAllDevices: {
      mutate: logoutAllDevicesMutation.mutateAsync,
      loading: logoutAllDevicesMutation.isPending,
      error: logoutAllDevicesMutation.error?.message || null,
    },
  };
}

// ============================================================================
// PROFILE UPDATE HOOK
// ============================================================================

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateSystemUserForm) => systemUserApi.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SYSTEM_USER_QUERY_KEYS.profile() });
      message.success('Profile updated successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || 'Failed to update profile');
    },
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook to check if current user has specific permission
 */
export function useSystemUserPermissions(currentUser?: SystemUser) {
  return {
    canCreateUsers: currentUser?.role === 'admin',
    canEditUsers: ['admin', 'editor'].includes(currentUser?.role || ''),
    canDeleteUsers: currentUser?.role === 'admin',
    canManageRoles: currentUser?.role === 'admin',
    canViewAllUsers: ['admin', 'editor'].includes(currentUser?.role || ''),
    canManageSystem: currentUser?.role === 'admin',
  };
}

/**
 * Hook to get role-based styling
 */
export function useRoleStyling() {
  return {
    getRoleColor: (role: string) => {
      const colors = {
        admin: 'red',
        editor: 'blue',
        moderator: 'green',
      };
      return colors[role as keyof typeof colors] || 'default';
    },
    getRoleIcon: (role: string) => {
      const icons = {
        admin: '👑',
        editor: '✏️',
        moderator: '🛡️',
      };
      return icons[role as keyof typeof icons] || '👤';
    },
  };
}
