// APISportsGame CMS - Auth API Module
// Authentication API calls

import axios, { AxiosInstance } from 'axios';
import { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';
import { apiUtils } from '@/shared/utils/api';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  SystemUser,
  RegisteredUser,
} from '../types';

// ============================================================================
// AUTH API ENDPOINTS
// ============================================================================

const AUTH_ENDPOINTS = {
  // SystemUser authentication endpoints
  login: '/system-auth/login',
  profile: '/system-auth/profile',
  refresh: '/system-auth/refresh',
  logout: '/system-auth/logout',
  logoutAll: '/system-auth/logout-all',
  createUser: '/system-auth/create-user',
  changePassword: '/system-auth/change-password',

  // RegisteredUser endpoints (for future User Manager module)
  userRegister: '/users/register',
  userLogin: '/users/login',
  verifyEmail: '/users/verify-email',
} as const;

// ============================================================================
// AUTH API CLIENT
// ============================================================================

class AuthApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - add JWT token
    this.instance.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(apiUtils.handleError(error))
    );

    // Response interceptor - handle errors
    this.instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors - token expired
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshToken();
            const token = this.getToken();
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            this.clearTokens();
            // Redirect to login will be handled by auth store
            throw apiUtils.handleError(refreshError);
          }
        }

        throw apiUtils.handleError(error);
      }
    );
  }

  // ========================================================================
  // TOKEN MANAGEMENT
  // ========================================================================

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
    }
  }

  private getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    }
    return null;
  }

  private setRefreshToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);
    }
  }

  private clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    }
  }

  // ========================================================================
  // AUTH API METHODS
  // ========================================================================

  /**
   * Login user (both SystemUser and RegisteredUser)
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await this.instance.post<LoginResponse>(
        AUTH_ENDPOINTS.login,
        credentials
      );

      const { accessToken, refreshToken } = response.data;
      this.setToken(accessToken);
      this.setRefreshToken(refreshToken);

      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<SystemUser | RegisteredUser> {
    try {
      const response = await this.instance.get<SystemUser | RegisteredUser>(
        AUTH_ENDPOINTS.profile
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await this.instance.post<RefreshTokenResponse>(
        AUTH_ENDPOINTS.refresh,
        { refresh_token: refreshToken }
      );

      const { accessToken, refreshToken: newRefreshToken } = response.data;
      this.setToken(accessToken);
      this.setRefreshToken(newRefreshToken);

      return response.data;
    } catch (error) {
      this.clearTokens();
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await this.instance.post(AUTH_ENDPOINTS.logout);
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  /**
   * Register new system user (admin only)
   */
  async registerSystemUser(userData: RegisterRequest & { role: string }): Promise<SystemUser> {
    try {
      const response = await this.instance.post<SystemUser>(
        AUTH_ENDPOINTS.adminRegister,
        userData
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Register new registered user
   */
  async registerUser(userData: RegisterRequest): Promise<RegisteredUser> {
    try {
      const response = await this.instance.post<RegisteredUser>(
        AUTH_ENDPOINTS.userRegister,
        userData
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Verify email for registered user
   */
  async verifyEmail(token: string): Promise<void> {
    try {
      await this.instance.post(AUTH_ENDPOINTS.verifyEmail, { token });
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  /**
   * Get current access token
   */
  getCurrentToken(): string | null {
    return this.getToken();
  }
}

// ============================================================================
// EXPORT SINGLETON INSTANCE
// ============================================================================

export const authApi = new AuthApiClient();
export default authApi;
