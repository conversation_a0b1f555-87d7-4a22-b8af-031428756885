// APISportsGame CMS - Format Utilities
// Utility functions cho formatting data

// ============================================================================
// NUMBER FORMATTING
// ============================================================================

/**
 * Format number với thousands separator
 */
export function formatNumber(num: number | null | undefined): string {
  if (num === null || num === undefined) return '0';
  return num.toLocaleString();
}

/**
 * Format currency
 */
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Format percentage
 */
export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// ============================================================================
// DATE FORMATTING
// ============================================================================

/**
 * Format date to readable string
 */
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return 'Invalid Date';
  
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Format date with time
 */
export function formatDateTime(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return 'Invalid Date';
  
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return 'Invalid Date';
  
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
  
  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
}

// ============================================================================
// STRING FORMATTING
// ============================================================================

/**
 * Truncate string với ellipsis
 */
export function truncateString(str: string, maxLength: number): string {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength - 3) + '...';
}

/**
 * Capitalize first letter
 */
export function capitalize(str: string): string {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert to title case
 */
export function toTitleCase(str: string): string {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

/**
 * Convert camelCase to readable string
 */
export function camelCaseToReadable(str: string): string {
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

// ============================================================================
// API USAGE FORMATTING
// ============================================================================

/**
 * Format API usage với percentage
 */
export function formatApiUsage(used: number, limit: number | null): {
  text: string;
  percentage: number;
  status: 'normal' | 'warning' | 'danger';
} {
  if (!limit) {
    return {
      text: `${formatNumber(used)} / Unlimited`,
      percentage: 0,
      status: 'normal',
    };
  }
  
  const percentage = (used / limit) * 100;
  let status: 'normal' | 'warning' | 'danger' = 'normal';
  
  if (percentage >= 90) status = 'danger';
  else if (percentage >= 70) status = 'warning';
  
  return {
    text: `${formatNumber(used)} / ${formatNumber(limit)}`,
    percentage: Math.min(percentage, 100),
    status,
  };
}

// ============================================================================
// TIER FORMATTING
// ============================================================================

/**
 * Format tier name với icon
 */
export function formatTierWithIcon(tier: string): string {
  const tierIcons = {
    free: '🆓',
    premium: '⭐',
    enterprise: '🏢',
  };
  
  const icon = tierIcons[tier as keyof typeof tierIcons] || '👤';
  return `${icon} ${tier.toUpperCase()}`;
}

// ============================================================================
// STATUS FORMATTING
// ============================================================================

/**
 * Format boolean status
 */
export function formatStatus(status: boolean, trueText = 'Active', falseText = 'Inactive'): {
  text: string;
  color: string;
} {
  return {
    text: status ? trueText : falseText,
    color: status ? 'success' : 'error',
  };
}

/**
 * Format subscription status
 */
export function formatSubscriptionStatus(endDate: Date | string | null): {
  text: string;
  color: string;
  isExpiring: boolean;
  isExpired: boolean;
} {
  if (!endDate) {
    return {
      text: 'No Subscription',
      color: 'default',
      isExpiring: false,
      isExpired: false,
    };
  }
  
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  const now = new Date();
  const daysUntilExpiry = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  if (daysUntilExpiry < 0) {
    return {
      text: 'Expired',
      color: 'error',
      isExpiring: false,
      isExpired: true,
    };
  }
  
  if (daysUntilExpiry <= 7) {
    return {
      text: `Expires in ${daysUntilExpiry} days`,
      color: 'warning',
      isExpiring: true,
      isExpired: false,
    };
  }
  
  return {
    text: 'Active',
    color: 'success',
    isExpiring: false,
    isExpired: false,
  };
}

// ============================================================================
// EXPORT ALL
// ============================================================================

export default {
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatFileSize,
  formatDate,
  formatDateTime,
  formatRelativeTime,
  truncateString,
  capitalize,
  toTitleCase,
  camelCaseToReadable,
  formatApiUsage,
  formatTierWithIcon,
  formatStatus,
  formatSubscriptionStatus,
};
