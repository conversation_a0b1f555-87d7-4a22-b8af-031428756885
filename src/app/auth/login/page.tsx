'use client';

// APISportsGame CMS - Login Page
// Dual authentication system theo CMS Development Guide

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Alert,
  Space,
  Divider,
  Row,
  Col
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  LoginOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { useAuth } from '@/stores/auth-store';
import { LoginRequest } from '@/types';

const { Title, Text } = Typography;

// ============================================================================
// LOGIN PAGE COMPONENT
// ============================================================================

export default function LoginPage() {
  const router = useRouter();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [form] = Form.useForm();

  // Redirect nếu đã đăng nhập
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Clear error khi component unmount
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  // ========================================================================
  // FORM SUBMIT HANDLER
  // ========================================================================
  const handleSubmit = async (values: LoginRequest) => {
    try {
      await login(values);
      // Redirect sẽ được xử lý bởi useEffect
    } catch (error) {
      // Error đã được xử lý trong store
      console.error('Login failed:', error);
    }
  };

  // ========================================================================
  // RENDER
  // ========================================================================
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Row justify="center" align="middle" className="w-full">
        <Col xs={24} sm={20} md={16} lg={12} xl={8}>
          <Card
            className="shadow-2xl border-0"
            bodyStyle={{ padding: '2rem' }}
          >
            {/* Header */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="bg-blue-500 p-3 rounded-full">
                  <ApiOutlined className="text-white text-2xl" />
                </div>
              </div>
              <Title level={2} className="mb-2">
                APISportsGame CMS
              </Title>
              <Text type="secondary" className="text-base">
                Đăng nhập vào hệ thống quản lý
              </Text>
            </div>

            {/* Error Alert */}
            {error && (
              <Alert
                message="Đăng nhập thất bại"
                description={error}
                type="error"
                showIcon
                closable
                onClose={clearError}
                className="mb-6"
              />
            )}

            {/* Login Form */}
            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
              autoComplete="off"
            >
              <Form.Item
                name="username"
                label="Tên đăng nhập"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập tên đăng nhập!'
                  },
                  {
                    min: 3,
                    message: 'Tên đăng nhập phải có ít nhất 3 ký tự!'
                  }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="Nhập tên đăng nhập"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="Mật khẩu"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập mật khẩu!'
                  },
                  {
                    min: 6,
                    message: 'Mật khẩu phải có ít nhất 6 ký tự!'
                  }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Nhập mật khẩu"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item className="mb-6">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  icon={<LoginOutlined />}
                  block
                  className="h-12 text-base font-medium"
                >
                  {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
                </Button>
              </Form.Item>
            </Form>

            <Divider>
              <Text type="secondary">Thông tin hệ thống</Text>
            </Divider>

            {/* System Info */}
            <div className="text-center space-y-2">
              <div className="bg-blue-50 p-4 rounded-lg">
                <Title level={5} className="mb-2 text-blue-700">
                  Dual Authentication System
                </Title>
                <Space direction="vertical" size="small">
                  <Text type="secondary" className="text-sm">
                    🔐 SystemUser: Admin, Editor, Moderator
                  </Text>
                  <Text type="secondary" className="text-sm">
                    👤 RegisteredUser: Free, Premium, Enterprise
                  </Text>
                </Space>
              </div>

              <Text type="secondary" className="text-xs">
                API Base URL: http://172.31.213.61
              </Text>
            </div>

            {/* Footer */}
            <div className="text-center mt-6 pt-4 border-t border-gray-100">
              <Text type="secondary" className="text-xs">
                APISportsGame CMS v1.0.0 - Phase 1 Implementation
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
