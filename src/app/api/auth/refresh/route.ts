// APISportsGame CMS - Auth Refresh Proxy Route
// Proxy route cho token refresh

import { NextRequest } from 'next/server';
import { createProxyHandler } from '../../lib/proxy';

// ============================================================================
// AUTH REFRESH PROXY
// ============================================================================

const handler = createProxyHandler('/system-auth/refresh');

export async function POST(request: NextRequest) {
  return handler(request);
}
