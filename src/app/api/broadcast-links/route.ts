// APISportsGame CMS - Broadcast Links Base Proxy Route
// Proxy route cho broadcast links base endpoint

import { NextRequest } from 'next/server';
import { createProxyHandler } from '../lib/proxy';

// ============================================================================
// BROADCAST LINKS BASE PROXY
// ============================================================================

const handler = createProxyHandler('/broadcast-links');

export async function GET(request: NextRequest) {
  return handler(request);
}

export async function POST(request: NextRequest) {
  return handler(request);
}
