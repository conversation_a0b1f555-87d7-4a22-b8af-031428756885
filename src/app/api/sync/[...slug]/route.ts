// APISportsGame CMS - Sync Proxy Route
// Dynamic proxy route cho sync endpoints

import { NextRequest } from 'next/server';
import { createDynamicProxyHandler } from '../../lib/proxy';

// ============================================================================
// SYNC PROXY
// ============================================================================

const handler = createDynamicProxyHandler('/sync');

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}
