// APISportsGame CMS - API Proxy Utility
// Utility functions cho API proxy routes

import { NextRequest, NextResponse } from 'next/server';

// ============================================================================
// TYPES
// ============================================================================

export interface ProxyConfig {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface ProxyOptions {
  method?: string;
  body?: any;
  headers?: Record<string, string>;
  searchParams?: URLSearchParams;
}

// ============================================================================
// PROXY UTILITY
// ============================================================================

export class ApiProxy {
  private baseUrl: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(config: ProxyConfig) {
    this.baseUrl = config.baseUrl;
    this.timeout = config.timeout || 10000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers,
    };
  }

  /**
   * Proxy request đến backend API
   */
  async proxyRequest(
    endpoint: string,
    options: ProxyOptions = {}
  ): Promise<NextResponse> {
    try {
      const url = new URL(endpoint, this.baseUrl);

      // Add search params nếu có
      if (options.searchParams) {
        options.searchParams.forEach((value, key) => {
          url.searchParams.append(key, value);
        });
      }

      const requestHeaders = {
        ...this.defaultHeaders,
        ...options.headers,
      };

      let requestBody: string | undefined;

      // Prepare body cho POST/PUT/PATCH requests
      if (options.body && ['POST', 'PUT', 'PATCH'].includes(options.method || 'GET')) {
        requestBody = typeof options.body === 'string'
          ? options.body
          : JSON.stringify(options.body);

        // Không set Content-Length manually, fetch sẽ tự động handle
      }

      const requestInit: RequestInit = {
        method: options.method || 'GET',
        headers: requestHeaders,
        body: requestBody,
        signal: AbortSignal.timeout(this.timeout),
      };

      console.log(`🔄 Proxying ${requestInit.method} ${url.toString()}`);
      console.log('📦 Request body:', requestBody);
      console.log('📋 Request headers:', requestHeaders);

      const response = await fetch(url.toString(), requestInit);

      // Get response data
      const contentType = response.headers.get('content-type');
      let data;

      if (contentType?.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      console.log(`✅ Proxy response: ${response.status} ${response.statusText}`);

      // Return NextResponse với same status và data
      return NextResponse.json(data, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          'Content-Type': contentType || 'application/json',
        },
      });

    } catch (error: any) {
      console.error('❌ Proxy error:', error);

      // Handle timeout
      if (error.name === 'TimeoutError') {
        return NextResponse.json(
          {
            error: 'Request timeout',
            message: 'The request took too long to complete',
            statusCode: 408
          },
          { status: 408 }
        );
      }

      // Handle network errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return NextResponse.json(
          {
            error: 'Network error',
            message: 'Unable to connect to the API server',
            statusCode: 503
          },
          { status: 503 }
        );
      }

      // Generic error
      return NextResponse.json(
        {
          error: 'Proxy error',
          message: error.message || 'An unknown error occurred',
          statusCode: 500
        },
        { status: 500 }
      );
    }
  }

  /**
   * Extract request data từ NextRequest
   */
  async extractRequestData(request: NextRequest): Promise<ProxyOptions> {
    const method = request.method;
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Extract headers (loại bỏ NextJS internal headers và browser headers)
    const headers: Record<string, string> = {};
    const allowedHeaders = ['content-type', 'accept', 'authorization'];

    request.headers.forEach((value, key) => {
      const lowerKey = key.toLowerCase();
      if (allowedHeaders.includes(lowerKey)) {
        headers[key] = value;
      }
    });

    // Extract body cho POST/PUT/PATCH
    let body;
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      const contentType = request.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        body = await request.json();
      } else {
        body = await request.text();
      }
    }

    return {
      method,
      headers,
      body,
      searchParams,
    };
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const apiProxy = new ApiProxy({
  baseUrl: process.env.API_BASE_URL || 'http://localhost:3000',
  timeout: 10000,
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Create proxy route handler
 */
export function createProxyHandler(endpoint: string) {
  return async function handler(request: NextRequest) {
    try {
      const options = await apiProxy.extractRequestData(request);
      return await apiProxy.proxyRequest(endpoint, options);
    } catch (error) {
      console.error('Proxy handler error:', error);
      return NextResponse.json(
        { error: 'Internal proxy error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Create dynamic proxy handler cho [...slug] routes
 */
export function createDynamicProxyHandler(basePath: string) {
  return async function handler(
    request: NextRequest,
    { params }: { params: { slug: string[] } }
  ) {
    try {
      const endpoint = `${basePath}/${params.slug.join('/')}`;
      const options = await apiProxy.extractRequestData(request);
      return await apiProxy.proxyRequest(endpoint, options);
    } catch (error) {
      console.error('Dynamic proxy handler error:', error);
      return NextResponse.json(
        { error: 'Internal proxy error' },
        { status: 500 }
      );
    }
  };
}
