// APISportsGame CMS - System Auth Proxy Route
// Proxy route for /system-auth/* endpoints

import { NextRequest } from 'next/server';
import { proxyRequest } from '../../lib/proxy';

// ============================================================================
// SYSTEM AUTH PROXY ROUTES
// ============================================================================

/**
 * Handle all HTTP methods for system-auth endpoints
 * Maps /api/system-auth/* to /system-auth/*
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/system-auth/${params.slug.join('/')}`;
  return proxyRequest(request, endpoint);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/system-auth/${params.slug.join('/')}`;
  return proxyRequest(request, endpoint);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/system-auth/${params.slug.join('/')}`;
  return proxyRequest(request, endpoint);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/system-auth/${params.slug.join('/')}`;
  return proxyRequest(request, endpoint);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/system-auth/${params.slug.join('/')}`;
  return proxyRequest(request, endpoint);
}
