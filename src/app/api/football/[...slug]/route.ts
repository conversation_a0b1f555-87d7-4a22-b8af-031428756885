// APISportsGame CMS - Football Proxy Route
// Dynamic proxy route cho tất cả football endpoints

import { NextRequest } from 'next/server';
import { createDynamicProxyHandler } from '../../lib/proxy';

// ============================================================================
// FOOTBALL PROXY
// ============================================================================

const handler = createDynamicProxyHandler('/football');

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}
