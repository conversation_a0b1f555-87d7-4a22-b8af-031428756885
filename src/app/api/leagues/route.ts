// APISportsGame CMS - Leagues Base Proxy Route
// Proxy route cho leagues base endpoint

import { NextRequest } from 'next/server';
import { createProxyHandler } from '../lib/proxy';

// ============================================================================
// LEAGUES BASE PROXY
// ============================================================================

const handler = createProxyHandler('/leagues');

export async function GET(request: NextRequest) {
  return handler(request);
}

export async function POST(request: NextRequest) {
  return handler(request);
}
