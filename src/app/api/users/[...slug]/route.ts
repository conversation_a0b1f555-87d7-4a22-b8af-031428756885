// APISportsGame CMS - Users Proxy Route
// Dynamic proxy route cho users endpoints

import { NextRequest } from 'next/server';
import { createDynamicProxyHandler } from '../../lib/proxy';

// ============================================================================
// USERS PROXY
// ============================================================================

const handler = createDynamicProxyHandler('/users');

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}
