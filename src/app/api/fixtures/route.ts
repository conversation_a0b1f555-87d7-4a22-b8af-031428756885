// APISportsGame CMS - Fixtures Base Proxy Route
// Proxy route cho fixtures base endpoint

import { NextRequest } from 'next/server';
import { createProxyHandler } from '../lib/proxy';

// ============================================================================
// FIXTURES BASE PROXY
// ============================================================================

const handler = createProxyHandler('/fixtures');

export async function GET(request: NextRequest) {
  return handler(request);
}

export async function POST(request: NextRequest) {
  return handler(request);
}
