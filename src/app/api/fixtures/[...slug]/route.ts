// APISportsGame CMS - Fixtures Proxy Route
// Dynamic proxy route cho fixtures endpoints

import { NextRequest } from 'next/server';
import { createDynamicProxyHandler } from '../../lib/proxy';

// ============================================================================
// FIXTURES PROXY
// ============================================================================

const handler = createDynamicProxyHandler('/fixtures');

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return handler(request, { params });
}
