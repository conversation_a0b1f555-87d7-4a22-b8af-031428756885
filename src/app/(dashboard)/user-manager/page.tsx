// APISportsGame CMS - User Manager Page
// Page for managing RegisteredUser (Free, Premium, Enterprise)

'use client';

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Alert,
  Typography,
  Empty,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  ApiOutlined,
  WarningOutlined,
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { usePermissions } from '@/stores/auth-store';

const { Title } = Typography;

// ============================================================================
// USER MANAGER PAGE (RegisteredUser Management)
// ============================================================================

export default function UserManagerPage() {
  const [loading, setLoading] = useState(false);

  // ========================================================================
  // PERMISSIONS
  // ========================================================================

  const permissions = usePermissions();

  // ========================================================================
  // MOCK DATA (To be replaced with real API calls)
  // ========================================================================

  const mockStats = {
    totalUsers: 0,
    activeUsers: 0,
    freeUsers: 0,
    premiumUsers: 0,
    enterpriseUsers: 0,
    usersNearLimit: 0,
  };

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  // ========================================================================
  // PERMISSION CHECK
  // ========================================================================

  if (!permissions.canManageUsers) {
    return (
      <Card>
        <Alert
          message="Access Denied"
          description="You don't have permission to manage registered users."
          type="error"
          showIcon
        />
      </Card>
    );
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-2">User Manager</Title>
          <p className="text-gray-600">Manage registered users, tiers, and API usage</p>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            disabled
          >
            Reset API Usage
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            Refresh
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            disabled
          >
            Add User
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Registered Users"
              value={mockStats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={mockStats.activeUsers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Free Users"
              value={mockStats.freeUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Premium Users"
              value={mockStats.premiumUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Enterprise Users"
              value={mockStats.enterpriseUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* API Usage Alert */}
      <Alert
        message="User Manager Module"
        description="This module will manage RegisteredUser (API consumers) with tiers: Free, Premium, Enterprise. Implementation coming soon."
        type="info"
        showIcon
        action={
          <Button size="small" type="primary" disabled>
            Configure
          </Button>
        }
      />

      {/* Main Content */}
      <Card title="Registered Users" loading={loading}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <p>No registered users found</p>
              <p className="text-gray-500 text-sm">
                This module will be implemented to manage API consumers
              </p>
            </div>
          }
        >
          <Button type="primary" disabled>
            Add First User
          </Button>
        </Empty>
      </Card>

      {/* Feature Preview */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="Tier Management" size="small">
            <div className="space-y-2">
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span>Free Tier</span>
                <span className="text-gray-500">100 API calls/month</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                <span>Premium Tier</span>
                <span className="text-yellow-600">10,000 API calls/month</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-purple-50 rounded">
                <span>Enterprise Tier</span>
                <span className="text-purple-600">Unlimited API calls</span>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="API Usage Monitoring" size="small">
            <div className="space-y-2">
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span>Total API Calls</span>
                <span className="text-gray-500">0</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
                <span>Average per User</span>
                <span className="text-blue-600">0</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-orange-50 rounded">
                <span>Users Near Limit</span>
                <span className="text-orange-600">0</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Implementation Notes */}
      <Card title="Implementation Plan" size="small">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>✅ Module structure created</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
            <span>🔄 API integration (pending)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
            <span>🔄 CRUD operations (pending)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
            <span>🔄 Tier management (pending)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
            <span>🔄 API usage analytics (pending)</span>
          </div>
        </div>
      </Card>
    </div>
  );
}
