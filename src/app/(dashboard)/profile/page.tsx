// APISportsGame CMS - Profile Page
// Complete profile page for current SystemUser

'use client';

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Button,
  Space,
  Avatar,
  Typography,
  Divider,
  Tag,
  Alert,
  Modal,
  Descriptions,
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  LockOutlined,
  LogoutOutlined,
  SaveOutlined,
  CloseOutlined,
  MailOutlined,
  IdcardOutlined,
  CrownOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import {
  useSystemUserProfile,
  useUpdateProfile,
  useSystemUserMutations,
} from '@/modules/system-users/hooks';
import {
  UpdateSystemUserForm,
  ChangePasswordForm,
} from '@/modules/system-users/types';
import { useAuth } from '@/stores/auth-store';
import { formatDate } from '@/shared/utils/format';

const { Title, Text } = Typography;

// ============================================================================
// PROFILE PAGE COMPONENT
// ============================================================================

export default function ProfilePage() {
  const [editMode, setEditMode] = useState(false);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();

  // ========================================================================
  // DATA & MUTATIONS
  // ========================================================================

  const { user: authUser, logout } = useAuth();
  const { data: profile, isLoading, refetch } = useSystemUserProfile();
  const updateProfileMutation = useUpdateProfile();
  const { changePassword, logoutAllDevices } = useSystemUserMutations();

  const currentUser = profile || authUser;

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleEditProfile = () => {
    setEditMode(true);
    profileForm.setFieldsValue({
      email: currentUser?.email,
      fullName: currentUser?.fullName || '',
    });
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    profileForm.resetFields();
  };

  const handleSaveProfile = async (values: UpdateSystemUserForm) => {
    try {
      await updateProfileMutation.mutateAsync(values);
      setEditMode(false);
      refetch();
    } catch (error) {
      console.error('Update profile error:', error);
    }
  };

  const handleChangePassword = async (values: ChangePasswordForm) => {
    try {
      await changePassword.mutate(values);
      setShowChangePassword(false);
      passwordForm.resetFields();
      
      // Auto logout after password change
      setTimeout(() => {
        logout();
      }, 2000);
    } catch (error) {
      console.error('Change password error:', error);
    }
  };

  const handleLogoutAllDevices = () => {
    Modal.confirm({
      title: 'Logout All Devices',
      content: 'Are you sure you want to logout from all devices? You will need to login again.',
      okText: 'Logout All',
      okType: 'danger',
      onOk: async () => {
        try {
          await logoutAllDevices.mutate();
          logout();
        } catch (error) {
          console.error('Logout all devices error:', error);
        }
      },
    });
  };

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const getRoleColor = (role: string) => {
    const colors = {
      admin: 'red',
      editor: 'blue',
      moderator: 'green',
    };
    return colors[role as keyof typeof colors] || 'default';
  };

  const getRoleIcon = (role: string) => {
    const icons = {
      admin: '👑',
      editor: '✏️',
      moderator: '🛡️',
    };
    return icons[role as keyof typeof icons] || '👤';
  };

  // ========================================================================
  // LOADING STATE
  // ========================================================================

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div>Loading profile...</div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <Alert
        message="Profile Not Found"
        description="Unable to load user profile information."
        type="error"
        showIcon
      />
    );
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-2">Profile</Title>
          <Text type="secondary">Manage your account information and security settings</Text>
        </div>
        <Space>
          <Button
            icon={<LogoutOutlined />}
            onClick={handleLogoutAllDevices}
            loading={logoutAllDevices.loading}
          >
            Logout All Devices
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        {/* Profile Information */}
        <Col span={16}>
          <Card title="Profile Information">
            {!editMode ? (
              <div>
                <Descriptions column={1} size="large">
                  <Descriptions.Item 
                    label={<><UserOutlined className="mr-2" />Username</>}
                  >
                    <Text strong>{currentUser.username}</Text>
                  </Descriptions.Item>
                  
                  <Descriptions.Item 
                    label={<><MailOutlined className="mr-2" />Email</>}
                  >
                    {currentUser.email}
                  </Descriptions.Item>
                  
                  <Descriptions.Item 
                    label={<><IdcardOutlined className="mr-2" />Full Name</>}
                  >
                    {currentUser.fullName || <Text type="secondary">Not provided</Text>}
                  </Descriptions.Item>
                  
                  <Descriptions.Item 
                    label={<><CrownOutlined className="mr-2" />Role</>}
                  >
                    <Tag color={getRoleColor(currentUser.role)} icon={getRoleIcon(currentUser.role)}>
                      {currentUser.role.toUpperCase()}
                    </Tag>
                  </Descriptions.Item>
                  
                  <Descriptions.Item 
                    label={<><CalendarOutlined className="mr-2" />Last Login</>}
                  >
                    {currentUser.lastLoginAt 
                      ? formatDate(currentUser.lastLoginAt)
                      : <Text type="secondary">Never</Text>
                    }
                  </Descriptions.Item>
                  
                  <Descriptions.Item 
                    label={<><CalendarOutlined className="mr-2" />Member Since</>}
                  >
                    {formatDate(currentUser.createdAt)}
                  </Descriptions.Item>
                </Descriptions>

                <Divider />
                
                <Space>
                  <Button 
                    type="primary" 
                    icon={<EditOutlined />} 
                    onClick={handleEditProfile}
                  >
                    Edit Profile
                  </Button>
                  <Button 
                    icon={<LockOutlined />} 
                    onClick={() => setShowChangePassword(true)}
                  >
                    Change Password
                  </Button>
                </Space>
              </div>
            ) : (
              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleSaveProfile}
                size="large"
              >
                <Form.Item
                  name="email"
                  label="Email"
                  rules={[
                    { required: true, message: 'Email is required' },
                    { type: 'email', message: 'Please enter a valid email' },
                  ]}
                >
                  <Input prefix={<MailOutlined />} />
                </Form.Item>

                <Form.Item
                  name="fullName"
                  label="Full Name"
                >
                  <Input prefix={<IdcardOutlined />} placeholder="Enter your full name" />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SaveOutlined />}
                      loading={updateProfileMutation.isPending}
                    >
                      Save Changes
                    </Button>
                    <Button
                      icon={<CloseOutlined />}
                      onClick={handleCancelEdit}
                    >
                      Cancel
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            )}
          </Card>
        </Col>

        {/* Profile Summary */}
        <Col span={8}>
          <Card>
            <div className="text-center">
              <Avatar 
                size={80} 
                icon={<UserOutlined />} 
                className="mb-4 bg-blue-500"
              />
              <Title level={4} className="mb-2">{currentUser.username}</Title>
              <Text type="secondary" className="block mb-4">{currentUser.email}</Text>
              
              <Tag 
                color={getRoleColor(currentUser.role)} 
                className="mb-4"
              >
                {getRoleIcon(currentUser.role)} {currentUser.role.toUpperCase()}
              </Tag>
              
              <div className="text-left">
                <Divider />
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text type="secondary">Status:</Text>
                    <Tag color={currentUser.isActive ? 'success' : 'error'}>
                      {currentUser.isActive ? 'Active' : 'Inactive'}
                    </Tag>
                  </div>
                  <div className="flex justify-between">
                    <Text type="secondary">User ID:</Text>
                    <Text>{currentUser.id}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text type="secondary">Created:</Text>
                    <Text>{formatDate(currentUser.createdAt)}</Text>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Security Actions */}
          <Card title="Security" className="mt-4">
            <div className="space-y-3">
              <Button 
                block 
                icon={<LockOutlined />} 
                onClick={() => setShowChangePassword(true)}
              >
                Change Password
              </Button>
              <Button 
                block 
                icon={<LogoutOutlined />} 
                onClick={handleLogoutAllDevices}
                loading={logoutAllDevices.loading}
              >
                Logout All Devices
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Change Password Modal */}
      <Modal
        title="Change Password"
        open={showChangePassword}
        onCancel={() => {
          setShowChangePassword(false);
          passwordForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Alert
          message="Security Notice"
          description="After changing your password, you will be logged out from all devices and need to login again."
          type="warning"
          showIcon
          className="mb-4"
        />

        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
          size="large"
        >
          <Form.Item
            name="currentPassword"
            label="Current Password"
            rules={[{ required: true, message: 'Current password is required' }]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="New Password"
            rules={[
              { required: true, message: 'New password is required' },
              { min: 8, message: 'Password must be at least 8 characters' },
            ]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="Confirm New Password"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: 'Please confirm your new password' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('Passwords do not match'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item>
            <Space className="w-full justify-end">
              <Button
                onClick={() => {
                  setShowChangePassword(false);
                  passwordForm.resetFields();
                }}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={changePassword.loading}
                danger
              >
                Change Password
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
