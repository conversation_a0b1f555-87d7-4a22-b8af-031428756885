// APISportsGame CMS - User Management Page
// Page cho quản lý RegisteredUser theo CMS_DEVELOPMENT_GUIDE.md

'use client';

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Modal,
  message,
  Tabs,
  Alert,
  Badge,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  ApiOutlined,
  WarningOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { RegisteredUserTable } from '@/modules/users/components/registered-user-table';
import { UserForm } from '@/modules/users/components/user-form';
import {
  useUsers,
  useUserMutations,
  useTierStatistics,
  useUsersApproachingLimits,
} from '@/modules/users/hooks';
import {
  RegisteredUser,
  CreateRegisteredUserForm,
  UpdateRegisteredUserForm,
  RegisteredUserFilters,
  TierUpgradeRequest,
  TierDowngradeRequest,
  ExtendSubscriptionRequest,
} from '@/modules/users/types';
import { usePermissions } from '@/stores/auth-store';

// ============================================================================
// USER MANAGEMENT PAGE
// ============================================================================

export default function UsersPage() {
  const [filters, setFilters] = useState<RegisteredUserFilters>({});
  const [pagination, setPagination] = useState({ page: 1, limit: 10 });
  const [selectedUser, setSelectedUser] = useState<RegisteredUser | null>(null);
  const [showUserForm, setShowUserForm] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // ========================================================================
  // PERMISSIONS & DATA
  // ========================================================================

  const permissions = usePermissions();
  const { users, loading, pagination: paginationData, refetch } = useUsers({
    filters,
    pagination,
  });
  const { data: tierStats } = useTierStatistics();
  const { data: approachingLimits } = useUsersApproachingLimits();
  const {
    createUser,
    updateUser,
    deleteUser,
    upgradeTier,
    downgradeTier,
    extendSubscription,
    resetApiUsage,
  } = useUserMutations();

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.isActive).length;
  const verifiedUsers = users.filter(user => user.isEmailVerified).length;
  const usersNearLimit = approachingLimits?.length || 0;

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleAddUser = () => {
    setSelectedUser(null);
    setFormMode('create');
    setShowUserForm(true);
  };

  const handleEditUser = (user: RegisteredUser) => {
    setSelectedUser(user);
    setFormMode('edit');
    setShowUserForm(true);
  };

  const handleDeleteUser = async (user: RegisteredUser) => {
    try {
      await deleteUser.mutate(user.id);
      refetch();
    } catch (error) {
      console.error('Delete user error:', error);
    }
  };

  const handleUpgradeTier = async (data: TierUpgradeRequest) => {
    try {
      await upgradeTier.mutate(data);
      refetch();
    } catch (error) {
      console.error('Upgrade tier error:', error);
    }
  };

  const handleDowngradeTier = async (data: TierDowngradeRequest) => {
    try {
      await downgradeTier.mutate(data);
      refetch();
    } catch (error) {
      console.error('Downgrade tier error:', error);
    }
  };

  const handleExtendSubscription = async (data: ExtendSubscriptionRequest) => {
    try {
      await extendSubscription.mutate(data);
      refetch();
    } catch (error) {
      console.error('Extend subscription error:', error);
    }
  };

  const handleViewUsage = (user: RegisteredUser) => {
    Modal.info({
      title: `API Usage - ${user.username}`,
      content: (
        <div className="space-y-4">
          <div>
            <strong>Current Usage:</strong> {user.apiCallsUsed.toLocaleString()} calls
          </div>
          <div>
            <strong>Limit:</strong> {user.apiCallsLimit?.toLocaleString() || 'Unlimited'} calls
          </div>
          <div>
            <strong>Tier:</strong> {user.tier.toUpperCase()}
          </div>
          <div>
            <strong>Percentage Used:</strong>{' '}
            {user.apiCallsLimit 
              ? `${((user.apiCallsUsed / user.apiCallsLimit) * 100).toFixed(1)}%`
              : '0%'
            }
          </div>
        </div>
      ),
      width: 500,
    });
  };

  const handleUserFormSubmit = async (data: CreateRegisteredUserForm | UpdateRegisteredUserForm) => {
    try {
      if (formMode === 'create') {
        await createUser.mutate(data as CreateRegisteredUserForm);
      } else if (selectedUser) {
        await updateUser.mutate(selectedUser.id, data as UpdateRegisteredUserForm);
      }
      setShowUserForm(false);
      setSelectedUser(null);
      refetch();
    } catch (error) {
      console.error('User form submit error:', error);
    }
  };

  const handleResetApiUsage = async () => {
    Modal.confirm({
      title: 'Reset API Usage',
      content: 'Are you sure you want to reset API usage for all users? This action cannot be undone.',
      okText: 'Reset',
      okType: 'danger',
      onOk: async () => {
        try {
          await resetApiUsage.mutate();
          refetch();
        } catch (error) {
          console.error('Reset API usage error:', error);
        }
      },
    });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ page, limit: pageSize });
  };

  // ========================================================================
  // PERMISSION CHECK
  // ========================================================================

  if (!permissions.canManageUsers) {
    return (
      <Card>
        <Alert
          message="Access Denied"
          description="You don't have permission to manage users."
          type="error"
          showIcon
        />
      </Card>
    );
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">User Management</h1>
          <p className="text-gray-600">Manage registered users, tiers, and API usage</p>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={handleResetApiUsage}
            loading={resetApiUsage.loading}
          >
            Reset API Usage
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={refetch}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={activeUsers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Verified Users"
              value={verifiedUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Near API Limit"
              value={usersNearLimit}
              prefix={<WarningOutlined />}
              valueStyle={{ color: usersNearLimit > 0 ? '#fa8c16' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Tier Statistics */}
      {tierStats && tierStats.length > 0 && (
        <Card title="Tier Distribution">
          <Row gutter={16}>
            {tierStats.map((stat) => (
              <Col span={8} key={stat.tier}>
                <Card size="small">
                  <Statistic
                    title={stat.tier.toUpperCase()}
                    value={stat.count}
                    suffix={`(${stat.percentage.toFixed(1)}%)`}
                    valueStyle={{ 
                      color: stat.tier === 'free' ? '#8c8c8c' : 
                             stat.tier === 'premium' ? '#faad14' : '#722ed1' 
                    }}
                  />
                  <div className="text-sm text-gray-500 mt-2">
                    Avg API calls: {stat.averageApiCalls.toLocaleString()}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* Users Approaching Limits Alert */}
      {usersNearLimit > 0 && (
        <Alert
          message={`${usersNearLimit} users are approaching their API limits`}
          description="Consider upgrading their tiers or extending their limits."
          type="warning"
          showIcon
          action={
            <Button size="small" onClick={() => setFilters({ ...filters, hasApiLimit: true })}>
              View Users
            </Button>
          }
        />
      )}

      {/* Users Table */}
      <RegisteredUserTable
        users={users}
        loading={loading}
        onEdit={handleEditUser}
        onDelete={handleDeleteUser}
        onUpgradeTier={handleUpgradeTier}
        onDowngradeTier={handleDowngradeTier}
        onExtendSubscription={handleExtendSubscription}
        onViewUsage={handleViewUsage}
        onAdd={handleAddUser}
        onRefresh={refetch}
        onExport={() => message.info('Export functionality coming soon')}
        pagination={{
          current: paginationData.current,
          pageSize: paginationData.pageSize,
          total: paginationData.total,
          onChange: handlePaginationChange,
        }}
        filters={filters}
        onFiltersChange={setFilters}
      />

      {/* User Form Modal */}
      <Modal
        title={formMode === 'create' ? 'Add New User' : 'Edit User'}
        open={showUserForm}
        onCancel={() => {
          setShowUserForm(false);
          setSelectedUser(null);
        }}
        footer={null}
        width={600}
      >
        <UserForm
          userType="registered"
          mode={formMode}
          initialValues={selectedUser || undefined}
          loading={createUser.loading || updateUser.loading}
          onSubmit={handleUserFormSubmit}
          onCancel={() => {
            setShowUserForm(false);
            setSelectedUser(null);
          }}
        />
      </Modal>
    </div>
  );
}
