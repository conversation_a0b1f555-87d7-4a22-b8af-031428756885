// APISportsGame CMS - System Users Management Page
// Page for managing SystemUser (<PERSON><PERSON>, Editor, Moderator)

'use client';

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Modal,
  Alert,
  Typography,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  useSystemUsers,
  useSystemUserMutations,
} from '@/modules/system-users/hooks';
import {
  SystemUser,
  CreateSystemUserForm,
  UpdateSystemUserForm,
  SystemUserFilters,
} from '@/modules/system-users/types';
import { usePermissions } from '@/stores/auth-store';

const { Title } = Typography;

// ============================================================================
// SYSTEM USERS MANAGEMENT PAGE
// ============================================================================

export default function SystemUsersPage() {
  const [filters, setFilters] = useState<SystemUserFilters>({});
  const [pagination, setPagination] = useState({ page: 1, limit: 10 });
  const [selectedUser, setSelectedUser] = useState<SystemUser | null>(null);
  const [showUserForm, setShowUserForm] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // ========================================================================
  // PERMISSIONS & DATA
  // ========================================================================

  const permissions = usePermissions();
  const { users, loading, pagination: paginationData, refetch } = useSystemUsers({
    filters,
    page: pagination.page,
    limit: pagination.limit,
  });
  
  const {
    createUser,
    updateUser,
    deleteUser,
    logoutAllDevices,
  } = useSystemUserMutations();

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.isActive).length;
  const adminUsers = users.filter(user => user.role === 'admin').length;
  const editorUsers = users.filter(user => user.role === 'editor').length;
  const moderatorUsers = users.filter(user => user.role === 'moderator').length;

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleAddUser = () => {
    setSelectedUser(null);
    setFormMode('create');
    setShowUserForm(true);
  };

  const handleEditUser = (user: SystemUser) => {
    setSelectedUser(user);
    setFormMode('edit');
    setShowUserForm(true);
  };

  const handleDeleteUser = async (user: SystemUser) => {
    Modal.confirm({
      title: 'Delete System User',
      content: `Are you sure you want to delete user "${user.username}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await deleteUser.mutate(user.id);
          refetch();
        } catch (error) {
          console.error('Delete user error:', error);
        }
      },
    });
  };

  const handleToggleUserStatus = async (user: SystemUser) => {
    try {
      await updateUser.mutate(user.id, { isActive: !user.isActive });
      refetch();
    } catch (error) {
      console.error('Toggle user status error:', error);
    }
  };

  const handleUserFormSubmit = async (data: CreateSystemUserForm | UpdateSystemUserForm) => {
    try {
      if (formMode === 'create') {
        await createUser.mutate(data as CreateSystemUserForm);
      } else if (selectedUser) {
        await updateUser.mutate(selectedUser.id, data as UpdateSystemUserForm);
      }
      setShowUserForm(false);
      setSelectedUser(null);
      refetch();
    } catch (error) {
      console.error('User form submit error:', error);
    }
  };

  const handleLogoutAllDevices = async () => {
    Modal.confirm({
      title: 'Logout All Devices',
      content: 'Are you sure you want to logout from all devices? You will need to login again.',
      okText: 'Logout All',
      okType: 'danger',
      onOk: async () => {
        try {
          await logoutAllDevices.mutate();
          // Redirect to login will be handled by the mutation
        } catch (error) {
          console.error('Logout all devices error:', error);
        }
      },
    });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ page, limit: pageSize });
  };

  // ========================================================================
  // PERMISSION CHECK
  // ========================================================================

  if (!permissions.canManageUsers) {
    return (
      <Card>
        <Alert
          message="Access Denied"
          description="You don't have permission to manage system users."
          type="error"
          showIcon
        />
      </Card>
    );
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-2">User System Management</Title>
          <p className="text-gray-600">Manage system users, roles, and permissions</p>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={handleLogoutAllDevices}
            loading={logoutAllDevices.loading}
          >
            Logout All Devices
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={refetch}
            loading={loading}
          >
            Refresh
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddUser}
          >
            Add System User
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total System Users"
              value={totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={activeUsers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Admins"
              value={adminUsers}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Editors"
              value={editorUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Moderators"
              value={moderatorUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* System Users Table */}
      <Card title="System Users" loading={loading}>
        {users.length === 0 ? (
          <div className="text-center py-8">
            <UserOutlined className="text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500">No system users found</p>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
              Add First System User
            </Button>
          </div>
        ) : (
          <div>
            {/* Table will be implemented in next step */}
            <p>System Users Table Component - To be implemented</p>
            <div className="space-y-2">
              {users.map((user) => (
                <div key={user.id} className="p-4 border rounded">
                  <div className="flex justify-between items-center">
                    <div>
                      <strong>{user.username}</strong> ({user.email})
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${
                        user.role === 'admin' ? 'bg-red-100 text-red-800' :
                        user.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {user.role.toUpperCase()}
                      </span>
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${
                        user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <Space>
                      <Button size="small" onClick={() => handleEditUser(user)}>
                        Edit
                      </Button>
                      <Button 
                        size="small" 
                        onClick={() => handleToggleUserStatus(user)}
                        type={user.isActive ? 'default' : 'primary'}
                      >
                        {user.isActive ? 'Deactivate' : 'Activate'}
                      </Button>
                      <Button 
                        size="small" 
                        danger 
                        onClick={() => handleDeleteUser(user)}
                      >
                        Delete
                      </Button>
                    </Space>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* User Form Modal - To be implemented */}
      <Modal
        title={formMode === 'create' ? 'Add System User' : 'Edit System User'}
        open={showUserForm}
        onCancel={() => {
          setShowUserForm(false);
          setSelectedUser(null);
        }}
        footer={null}
        width={600}
      >
        <p>System User Form Component - To be implemented</p>
      </Modal>
    </div>
  );
}
