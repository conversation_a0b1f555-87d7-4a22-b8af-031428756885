// APISportsGame CMS - System Users Management Page
// Page for managing SystemUser (<PERSON><PERSON>, Editor, Moderator)

'use client';

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Modal,
  Alert,
  Typography,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  useSystemUsers,
  useSystemUserMutations,
} from '@/modules/system-users/hooks';
import {
  SystemUser,
  CreateSystemUserForm,
  UpdateSystemUserForm,
  SystemUserFilters,
} from '@/modules/system-users/types';
import { SystemUserTable } from '@/modules/system-users/components/system-user-table';
import { SystemUserForm } from '@/modules/system-users/components/system-user-form';
import { usePermissions } from '@/stores/auth-store';

const { Title } = Typography;

// ============================================================================
// SYSTEM USERS MANAGEMENT PAGE
// ============================================================================

export default function SystemUsersPage() {
  const [filters, setFilters] = useState<SystemUserFilters>({});
  const [pagination, setPagination] = useState({ page: 1, limit: 10 });
  const [selectedUser, setSelectedUser] = useState<SystemUser | null>(null);
  const [showUserForm, setShowUserForm] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // ========================================================================
  // PERMISSIONS & DATA
  // ========================================================================

  const permissions = usePermissions();
  const { users, loading, pagination: paginationData, refetch } = useSystemUsers({
    filters,
    page: pagination.page,
    limit: pagination.limit,
  });

  const {
    createUser,
    updateUser,
    deleteUser,
    logoutAllDevices,
  } = useSystemUserMutations();

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.isActive).length;
  const adminUsers = users.filter(user => user.role === 'admin').length;
  const editorUsers = users.filter(user => user.role === 'editor').length;
  const moderatorUsers = users.filter(user => user.role === 'moderator').length;

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleAddUser = () => {
    setSelectedUser(null);
    setFormMode('create');
    setShowUserForm(true);
  };

  const handleEditUser = (user: SystemUser) => {
    setSelectedUser(user);
    setFormMode('edit');
    setShowUserForm(true);
  };

  const handleDeleteUser = async (user: SystemUser) => {
    Modal.confirm({
      title: 'Delete System User',
      content: `Are you sure you want to delete user "${user.username}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await deleteUser.mutate(user.id);
          refetch();
        } catch (error) {
          console.error('Delete user error:', error);
        }
      },
    });
  };

  const handleToggleUserStatus = async (user: SystemUser) => {
    try {
      await updateUser.mutate(user.id, { isActive: !user.isActive });
      refetch();
    } catch (error) {
      console.error('Toggle user status error:', error);
    }
  };

  const handleResetPassword = async (user: SystemUser) => {
    // This would call a reset password API
    Modal.info({
      title: 'Password Reset',
      content: `Password reset functionality for ${user.username} will be implemented.`,
    });
  };

  const handleExport = () => {
    // Export functionality
    Modal.info({
      title: 'Export Users',
      content: 'Export functionality will be implemented.',
    });
  };

  const handleUserFormSubmit = async (data: CreateSystemUserForm | UpdateSystemUserForm) => {
    try {
      if (formMode === 'create') {
        await createUser.mutate(data as CreateSystemUserForm);
      } else if (selectedUser) {
        await updateUser.mutate(selectedUser.id, data as UpdateSystemUserForm);
      }
      setShowUserForm(false);
      setSelectedUser(null);
      refetch();
    } catch (error) {
      console.error('User form submit error:', error);
    }
  };

  const handleLogoutAllDevices = async () => {
    Modal.confirm({
      title: 'Logout All Devices',
      content: 'Are you sure you want to logout from all devices? You will need to login again.',
      okText: 'Logout All',
      okType: 'danger',
      onOk: async () => {
        try {
          await logoutAllDevices.mutate();
          // Redirect to login will be handled by the mutation
        } catch (error) {
          console.error('Logout all devices error:', error);
        }
      },
    });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ page, limit: pageSize });
  };

  // ========================================================================
  // PERMISSION CHECK
  // ========================================================================

  if (!permissions.canManageUsers) {
    return (
      <Card>
        <Alert
          message="Access Denied"
          description="You don't have permission to manage system users."
          type="error"
          showIcon
        />
      </Card>
    );
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-2">User System Management</Title>
          <p className="text-gray-600">Manage system users, roles, and permissions</p>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={handleLogoutAllDevices}
            loading={logoutAllDevices.loading}
          >
            Logout All Devices
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={refetch}
            loading={loading}
          >
            Refresh
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddUser}
          >
            Add System User
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total System Users"
              value={totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={activeUsers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Admins"
              value={adminUsers}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Editors"
              value={editorUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Moderators"
              value={moderatorUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* System Users Table */}
      <SystemUserTable
        users={users}
        loading={loading}
        onEdit={handleEditUser}
        onDelete={handleDeleteUser}
        onToggleStatus={handleToggleUserStatus}
        onResetPassword={handleResetPassword}
        onAdd={handleAddUser}
        onRefresh={refetch}
        onExport={handleExport}
        pagination={{
          current: paginationData.current,
          pageSize: paginationData.pageSize,
          total: paginationData.total,
          onChange: handlePaginationChange,
        }}
        filters={filters}
        onFiltersChange={setFilters}
      />

      {/* User Form Modal */}
      <Modal
        title={formMode === 'create' ? 'Add System User' : 'Edit System User'}
        open={showUserForm}
        onCancel={() => {
          setShowUserForm(false);
          setSelectedUser(null);
        }}
        footer={null}
        width={800}
        destroyOnClose
      >
        <SystemUserForm
          user={selectedUser || undefined}
          mode={formMode}
          loading={createUser.loading || updateUser.loading}
          onSubmit={handleUserFormSubmit}
          onCancel={() => {
            setShowUserForm(false);
            setSelectedUser(null);
          }}
        />
      </Modal>
    </div>
  );
}
