// APISportsGame CMS - Authentication Store
// Zustand store cho SystemUser authentication (FECMS sử dụng SystemUser)

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authApi } from '@/modules/auth/api';
import {
  SystemUser,
  RegisteredUser,
  LoginRequest,
  AuthState,
  AuthActions,
  UseAuthReturn,
  UsePermissionsReturn,
  isSystemUser,
  isRegisteredUser,
} from '@/modules/auth/types';

// ============================================================================
// AUTH STORE TYPES
// ============================================================================

interface AuthStoreState extends AuthState, AuthActions {
  // Helpers
  isSystemUser: () => boolean;
  isRegisteredUser: () => boolean;
  hasRole: (role: string) => boolean;
  hasTier: (tier: string) => boolean;
}

// ============================================================================
// AUTH STORE IMPLEMENTATION
// ============================================================================

export const useAuthStore = create<AuthStoreState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // ========================================================================
      // LOGIN ACTION
      // ========================================================================
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authApi.login(credentials);

          // Nếu response có user, sử dụng nó. Nếu không, gọi getProfile
          let user = response.user;
          if (!user) {
            user = await authApi.getProfile();
          }

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message ||
            error.message ||
            'Đăng nhập thất bại';

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });

          throw error;
        }
      },

      // ========================================================================
      // LOGOUT ACTION
      // ========================================================================
      logout: () => {
        try {
          authApi.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      // ========================================================================
      // GET PROFILE ACTION
      // ========================================================================
      getProfile: async () => {
        set({ isLoading: true, error: null });

        try {
          const user = await authApi.getProfile();

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message ||
            error.message ||
            'Không thể lấy thông tin người dùng';

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });

          throw error;
        }
      },

      // ========================================================================
      // CLEAR ERROR ACTION
      // ========================================================================
      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // HELPER METHODS
      // ========================================================================
      isSystemUser: () => {
        const { user } = get();
        return isSystemUser(user);
      },

      isRegisteredUser: () => {
        const { user } = get();
        return isRegisteredUser(user);
      },

      hasRole: (role: string) => {
        const { user, isSystemUser } = get();
        if (!isSystemUser() || !user) return false;

        const systemUser = user as SystemUser;
        return systemUser.role === role;
      },

      hasTier: (tier: string) => {
        const { user, isRegisteredUser } = get();
        if (!isRegisteredUser() || !user) return false;

        const registeredUser = user as RegisteredUser;
        return registeredUser.tier === tier;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// ============================================================================
// AUTH HOOKS
// ============================================================================

// Hook để check permissions
export const usePermissions = () => {
  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();

  return {
    // User type checks
    isSystemUser: isSystemUser(),
    isRegisteredUser: isRegisteredUser(),

    // Role checks (SystemUser)
    isAdmin: hasRole('admin'),
    isEditor: hasRole('editor'),
    isModerator: hasRole('moderator'),

    // Tier checks (RegisteredUser)
    isFree: hasTier('free'),
    isPremium: hasTier('premium'),
    isEnterprise: hasTier('enterprise'),

    // Permission checks
    canManageUsers: hasRole('admin'),
    canManageLeagues: hasRole('admin') || hasRole('editor'),
    canManageTeams: hasRole('admin') || hasRole('editor'),
    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),
    canSync: hasRole('admin') || hasRole('editor'),
    canViewAnalytics: hasRole('admin') || hasRole('editor'),

    // Current user
    currentUser: user,
  };
};

// Hook để check authentication status
export const useAuth = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    getProfile,
    clearError
  } = useAuthStore();

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    getProfile,
    clearError,
  };
};
