# APISportsGame CMS Development Guide

> **Living Document**: This file is continuously updated during APISportsGame backend development to provide real-time information for Frontend CMS development.

## 📋 **Document Purpose**

This document serves as the **single source of truth** for AI agents developing the Frontend CMS. It contains:
- Current API endpoints and schemas
- Data models and relationships
- Business logic and workflows
- Authentication and authorization rules
- Real-time development status

---

## 🏗️ **Project Architecture Overview**

### **Backend Services:**
```
APISportsGame (main.ts)     # HTTP API Server (Port 3000)
AutoUpdateSportsGame (worker.ts)  # Background Worker Service
```

### **Database:** PostgreSQL with TypeORM
### **Authentication:** Dual system (SystemUser + RegisteredUser)
### **API Documentation:** Swagger at `/api-docs`

---

## 🔐 **Authentication System**

### **1. SystemUser (Admin/Internal)**
```typescript
interface SystemUser {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'editor' | 'moderator';
  isActive: boolean;
  lastLoginAt: Date;
  createdAt: Date;
}
```

**Endpoints:**
```
POST /system-auth/login       # System user login
POST /system-auth/create-user # Create system user (admin only)
GET  /system-auth/profile     # Get current user profile
POST /system-auth/refresh     # Refresh access token
POST /system-auth/logout      # Logout and revoke tokens
POST /system-auth/logout-all  # Logout from all devices
```

### **2. RegisteredUser (End Users/Customers)**
```typescript
interface RegisteredUser {
  id: number;
  username: string;
  email: string;
  tier: 'free' | 'premium' | 'enterprise';
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  subscriptionEndDate: Date | null;
  lastLoginAt: Date;
  createdAt: Date;
}
```

**Endpoints:**
```
POST /users/register            # User registration
POST /users/login              # User login
POST /users/verify-email       # Email verification
POST /users/resend-verification # Resend email verification
POST /users/forgot-password    # Request password reset
POST /users/reset-password     # Reset password with token
GET  /users/profile            # Get user profile
PUT  /users/profile            # Update user profile
POST /users/change-password    # Change password
GET  /users/api-usage          # API usage statistics
```

### **3. JWT Token Structure**
```typescript
// SystemUser Token
{
  sub: number;
  username: string;
  email: string;
  role: SystemRole;
  userType: 'system';
}

// RegisteredUser Token
{
  sub: number;
  username: string;
  email: string;
  tier: RegisteredUserTier;
  userType: 'registered';
  isEmailVerified: boolean;
}
```

---

## ⚽ **Sports Data Models**

### **1. League Entity**
```typescript
interface League {
  id: number;
  apiFootballId: number;
  name: string;
  country: string;
  logo: string;
  flag: string;
  season: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### **2. Team Entity**
```typescript
interface Team {
  id: number;
  apiFootballId: number;
  name: string;
  code: string;
  country: string;
  founded: number;
  logo: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### **3. Fixture Entity**
```typescript
interface Fixture {
  id: number;
  apiFootballId: number;
  referee: string;
  timezone: string;
  date: Date;
  timestamp: number;

  // Venue
  venueId: number;
  venueName: string;
  venueCity: string;

  // Status
  statusLong: string;
  statusShort: string;
  elapsed: number;

  // League
  leagueId: number;
  leagueName: string;
  leagueSeason: number;
  leagueRound: string;

  // Teams
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;

  // Score
  goalsHome: number;
  goalsAway: number;
  scoreHalftimeHome: number;
  scoreHalftimeAway: number;
  scoreFulltimeHome: number;
  scoreFulltimeAway: number;

  createdAt: Date;
  updatedAt: Date;
}
```

---

## 🌐 **API Endpoints**

### **1. League Management**
```
GET    /football/leagues           # List all leagues
GET    /football/leagues/:externalId # Get league by external ID
POST   /football/leagues           # Create league (admin only)
PATCH  /football/leagues/:id       # Update league (admin only)
```

### **2. Team Management**
```
GET    /football/teams             # List all teams
GET    /football/teams/statistics  # Get team statistics
GET    /football/teams/:externalId # Get team by external ID
```

### **3. Fixture Management**
```
GET    /football/fixtures/upcoming-and-live    # Get upcoming and live fixtures
GET    /football/fixtures/sync/fixtures        # Trigger season fixtures sync (admin)
GET    /football/fixtures/sync/daily           # Trigger daily sync
GET    /football/fixtures/sync/status          # Get sync status
GET    /football/fixtures/schedules/:teamId    # Get team schedule
GET    /football/fixtures/statistics/:externalId # Get fixture statistics
GET    /football/fixtures                      # List fixtures with filters
GET    /football/fixtures/:externalId          # Get fixture by external ID
POST   /football/fixtures                      # Create fixture (admin only)
PATCH  /football/fixtures/:externalId          # Update fixture (admin only)
```

### **4. Admin User Management**
```
GET    /admin/tiers/statistics                    # Get tier statistics
GET    /admin/users/approaching-limits            # Get users approaching API limits
POST   /admin/users/:userId/upgrade-tier          # Upgrade user tier
POST   /admin/users/:userId/downgrade-tier        # Downgrade user tier
POST   /admin/users/:userId/extend-subscription   # Extend user subscription
POST   /admin/reset-api-usage                     # Reset monthly API usage
POST   /admin/check-usage-warnings                # Check API usage warnings
GET    /admin/users/:userId/subscription          # Get user subscription info
GET    /admin/users                               # Get all registered users
```

### **5. Broadcast Links**
```
POST   /broadcast-links                           # Create broadcast link
GET    /broadcast-links/fixture/:fixtureId       # Get broadcast links by fixture
PATCH  /broadcast-links/:id                      # Update broadcast link
DELETE /broadcast-links/:id                      # Delete broadcast link
```

---

## 🔒 **Authorization Rules**

### **SystemUser Permissions:**
```typescript
Admin: {
  - Full CRUD on all entities
  - User management
  - Sync operations
  - System configuration
}

Editor: {
  - CRUD on leagues, teams, fixtures
  - Sync operations
  - View users
}

Moderator: {
  - Update fixtures
  - View operations only
}
```

### **RegisteredUser Permissions:**
```typescript
Free: {
  - View leagues, teams, fixtures
  - 100 API calls/month
  - Basic profile management
}

Premium: {
  - All Free features
  - 10,000 API calls/month
  - Advanced filtering
  - Export data
}

Enterprise: {
  - All Premium features
  - Unlimited API calls
  - Priority support
  - Custom integrations
}
```

---

## 📊 **CMS Requirements**

### **1. Dashboard Features:**
- System overview (users, API calls, sync status)
- Real-time statistics
- Recent activities
- System health monitoring

### **2. User Management:**
- SystemUser CRUD operations
- RegisteredUser management
- Role assignment
- Activity monitoring
- API usage analytics

### **3. Sports Data Management:**
- League management with logo upload
- Team management with logo upload
- Fixture management with bulk operations
- Sync status monitoring
- Data validation and cleanup

### **4. API Management:**
- Endpoint monitoring
- Rate limiting configuration
- Usage analytics
- Error tracking
- Performance metrics

---

## 🛠️ **Technical Specifications**

### **1. API Base URL:** `http://localhost:3000`
### **2. Swagger Documentation:** `http://localhost:3000/api-docs`
### **3. Authentication:** Bearer JWT tokens
### **4. Content-Type:** `application/json`
### **5. Error Format:**
```typescript
{
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}
```

### **6. Pagination Format:**
```typescript
{
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }
}
```

---

## 🔄 **Development Status**

### **✅ Completed Features:**
- [x] SystemUser authentication system
- [x] RegisteredUser authentication system
- [x] League, Team, Fixture entities
- [x] Basic CRUD operations
- [x] Sync system with API-Football
- [x] JWT dual authentication
- [x] Rate limiting and security
- [x] Swagger documentation
- [x] RegisteredUser Controller implementation
- [x] Tier-based access control guards
- [x] API usage tracking interceptors
- [x] Email service integration (configured)
- [x] Admin seeder service
- [x] Audit logging system

### **🚧 In Progress:**
- [ ] Advanced analytics dashboard
- [ ] Subscription management
- [ ] Image upload service
- [ ] WebSocket real-time updates

### **📋 Planned Features:**
- [ ] Advanced search and filtering
- [ ] Data export functionality
- [ ] Performance monitoring dashboard
- [ ] Multi-language support

---

## 📝 **Notes for CMS Development**

### **1. Priority Order:**
1. Authentication integration
2. User management interface
3. Sports data management
4. Dashboard and analytics
5. Advanced features

### **2. Key Considerations:**
- Responsive design (mobile-first)
- Real-time data updates
- Bulk operations support
- Error handling and validation
- Performance optimization

### **3. Recommended Tech Stack:**
- React 18 + TypeScript
- Ant Design Pro
- React Query for API calls
- Zustand for state management
- Recharts for analytics

---

## 🧪 **Latest Test Results (2025-05-24)**

### **✅ AUTH System Test Results:**
- **SystemUser Login**: ✅ Working (admin/admin123456)
- **JWT Token Generation**: ✅ Working
- **Profile Endpoint**: ✅ Working
- **RegisteredUser Registration**: ✅ Working
- **Token Refresh**: ✅ Working
- **Authorization Guards**: ✅ Working

### **✅ API Endpoints Test Results:**
- **Swagger Documentation**: ✅ Available at `/api-docs`
- **Football Leagues**: ✅ Working (`/football/leagues` - 18 leagues found)
- **Football Fixtures**: ✅ Working (`/football/fixtures/upcoming-and-live`)
- **RegisteredUser Registration**: ✅ Working (`/users/register`)
- **Public Endpoints**: ✅ No auth required
- **Protected Endpoints**: ✅ JWT auth required

### **✅ System Health:**
- **Database Connection**: ✅ PostgreSQL connected
- **UTC Timezone**: ✅ Properly configured
- **Admin Seeder**: ✅ Working
- **Email Service**: ⚠️ Configured (SMTP not connected)
- **Rate Limiting**: ✅ Active

---

**Last Updated:** 2025-05-24
**Version:** 2.0.0
**Status:** Active Development - Phase 20+ Complete - AUTH System Tested & Verified
