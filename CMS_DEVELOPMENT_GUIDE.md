# APISportsGame CMS Development Guide

> **Living Document**: This file is continuously updated during APISportsGame backend development to provide real-time information for Frontend CMS development.

## 📋 **Document Purpose**

This document serves as the **single source of truth** for AI agents developing the Frontend CMS. It contains:
- Current API endpoints and schemas
- Data models and relationships
- Business logic and workflows
- Authentication and authorization rules
- Real-time development status

---

## 🏗️ **Project Architecture Overview**

### **Backend Services:**
```
APISportsGame (main.ts)         # HTTP API Server (Port 3000)
AutoUpdateSportsGame (worker.ts) # Background Worker Service
```

### **Database:**
PostgreSQL with TypeORM

### **Authentication:**
Dual system (SystemUser + RegisteredUser)

### **API Documentation:**
Swagger at `/api/docs`

---

## 🔐 **Authentication System**

### **1. SystemUser (Admin/Internal)**
```typescript
interface SystemUser {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'editor' | 'moderator';
  isActive: boolean;
  lastLoginAt: Date;
  createdAt: Date;
}
```

**Endpoints:**
```
POST /auth/login           # System user login
POST /auth/admin/register  # Create system user (admin only)
GET /auth/profile          # Get current user profile
POST /auth/refresh         # Refresh access token
POST /auth/logout          # Logout and revoke tokens
```

### **2. RegisteredUser (End Users/Customers)**
```typescript
interface RegisteredUser {
  id: number;
  username: string;
  email: string;
  tier: 'free' | 'premium' | 'enterprise';
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  subscriptionEndDate: Date | null;
  lastLoginAt: Date;
  createdAt: Date;
}
```

**Endpoints:**
```
POST /users/register    # User registration
POST /users/login       # User login
POST /users/verify-email # Email verification
GET /users/profile      # Get user profile
PUT /users/profile      # Update user profile
GET /users/api-usage    # API usage statistics
```

### **3. JWT Token Structure**
```typescript
// SystemUser Token
{
  sub: number;
  username: string;
  email: string;
  role: SystemRole;
  userType: 'system';
}

// RegisteredUser Token
{
  sub: number;
  username: string;
  email: string;
  tier: RegisteredUserTier;
  userType: 'registered';
  isEmailVerified: boolean;
}
```

---

## ⚽ **Sports Data Models**

### **1. League Entity**
```typescript
interface League {
  id: number;
  apiFootballId: number;
  name: string;
  country: string;
  logo: string;
  flag: string;
  season: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### **2. Team Entity**
```typescript
interface Team {
  id: number;
  apiFootballId: number;
  name: string;
  code: string;
  country: string;
  founded: number;
  logo: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### **3. Fixture Entity**
```typescript
interface Fixture {
  id: number;
  apiFootballId: number;
  referee: string;
  timezone: string;
  date: Date;
  timestamp: number;
  
  // Venue
  venueId: number;
  venueName: string;
  venueCity: string;
  
  // Status
  statusLong: string;
  statusShort: string;
  elapsed: number;
  
  // League
  leagueId: number;
  leagueName: string;
  leagueSeason: number;
  leagueRound: string;
  
  // Teams
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  
  // Score
  goalsHome: number;
  goalsAway: number;
  scoreHalftimeHome: number;
  scoreHalftimeAway: number;
  scoreFulltimeHome: number;
  scoreFulltimeAway: number;
  
  createdAt: Date;
  updatedAt: Date;
}
```

---

## 🌐 **API Endpoints**

### **1. League Management**
```
GET /leagues              # List all leagues
GET /leagues/active       # List active leagues
GET /leagues/:id          # Get league by ID
POST /leagues             # Create league (admin only)
PUT /leagues/:id          # Update league (admin only)
DELETE /leagues/:id       # Delete league (admin only)
GET /leagues/:id/fixtures # Get league fixtures
```

### **2. Team Management**
```
GET /teams                # List all teams
GET /teams/:id            # Get team by ID
POST /teams               # Create team (admin only)
PUT /teams/:id            # Update team (admin only)
DELETE /teams/:id         # Delete team (admin only)
GET /teams/:id/fixtures   # Get team fixtures
```

### **3. Fixture Management**
```
GET /fixtures             # List fixtures with filters
GET /fixtures/:id         # Get fixture by ID
GET /fixtures/live        # Get live fixtures
GET /fixtures/today       # Get today's fixtures
GET /fixtures/date/:date  # Get fixtures by date
POST /fixtures            # Create fixture (admin only)
PUT /fixtures/:id         # Update fixture (admin only)
DELETE /fixtures/:id      # Delete fixture (admin only)
```

### **4. Sync Operations**
```
POST /sync/leagues        # Sync leagues from API-Football
POST /sync/teams          # Sync teams from API-Football
POST /sync/fixtures       # Sync fixtures from API-Football
POST /sync/daily          # Trigger daily sync
GET /sync/status          # Get sync status
```

---

## 🔒 **Authorization Rules**

### **SystemUser Permissions:**
```typescript
Admin: {
  - Full CRUD on all entities
  - User management
  - Sync operations
  - System configuration
}

Editor: {
  - CRUD on leagues, teams, fixtures
  - Sync operations
  - View users
}

Moderator: {
  - Update fixtures
  - View operations only
}
```

### **RegisteredUser Permissions:**
```typescript
Free: {
  - View leagues, teams, fixtures
  - 100 API calls/month
  - Basic profile management
}

Premium: {
  - All Free features
  - 10,000 API calls/month
  - Advanced filtering
  - Export data
}

Enterprise: {
  - All Premium features
  - Unlimited API calls
  - Priority support
  - Custom integrations
}
```

---

## 📊 **CMS Requirements**

### **1. Dashboard Features:**
- System overview (users, API calls, sync status)
- Real-time statistics
- Recent activities
- System health monitoring

### **2. User Management:**
- SystemUser CRUD operations
- RegisteredUser management
- Role assignment
- Activity monitoring
- API usage analytics

### **3. Sports Data Management:**
- League management with logo upload
- Team management with logo upload
- Fixture management with bulk operations
- Sync status monitoring
- Data validation and cleanup

### **4. API Management:**
- Endpoint monitoring
- Rate limiting configuration
- Usage analytics
- Error tracking
- Performance metrics

---

## 🛠️ **Technical Specifications**

### **1. API Base URL:**
`http://localhost:3000`

### **2. Authentication:**
Bearer JWT tokens

### **3. Content-Type:**
`application/json`

### **4. Error Format:**
```typescript
{
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}
```

### **5. Pagination Format:**
```typescript
{
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }
}
```

---

## 🔄 **Development Status**

### **✅ Completed Features:**
- [x] SystemUser authentication system
- [x] RegisteredUser authentication system
- [x] League, Team, Fixture entities
- [x] Basic CRUD operations
- [x] Sync system with API-Football
- [x] JWT dual authentication
- [x] Rate limiting and security
- [x] Swagger documentation
- [x] RegisteredUser Controller implementation
- [x] Tier-based access control guards
- [x] API usage tracking interceptors
- [x] Email service integration (configured)
- [x] Admin seeder service
- [x] Audit logging system

### **🚧 In Progress:**
- [ ] Advanced analytics dashboard
- [ ] Subscription management
- [ ] Image upload service
- [ ] WebSocket real-time updates

### **📋 Planned Features:**
- [ ] Advanced search and filtering
- [ ] Data export functionality
- [ ] Performance monitoring dashboard
- [ ] Multi-language support

---

## 📝 **Notes for CMS Development**

### **1. Priority Order:**
1. Authentication integration
2. User management interface
3. Sports data management
4. Dashboard and analytics
5. Advanced features

### **2. Key Considerations:**
- Responsive design (mobile-first)
- Real-time data updates
- Bulk operations support
- Error handling and validation
- Performance optimization

### **3. Recommended Tech Stack:**
- React 18 + TypeScript
- Ant Design Pro
- React Query for API calls
- Zustand for state management
- Recharts for analytics

---

## 🧪 **Latest Test Results (2025-05-24)**

### **✅ AUTH System Test Results:**
- **SystemUser Login**: ✅ Working (admin/admin123456)
- **JWT Token Generation**: ✅ Working
- **Profile Endpoint**: ✅ Working
- **RegisteredUser Registration**: ✅ Working
- **Token Refresh**: ✅ Working
- **Authorization Guards**: ✅ Working

### **✅ API Endpoints Test Results:**
- **Swagger Documentation**: ✅ Available at `/api-docs`
- **Football Leagues**: ✅ Working (18 leagues found)
- **Football Fixtures**: ✅ Working
- **Public Endpoints**: ✅ No auth required
- **Protected Endpoints**: ✅ JWT auth required

### **✅ System Health:**
- **Database Connection**: ✅ PostgreSQL connected
- **UTC Timezone**: ✅ Properly configured
- **Admin Seeder**: ✅ Working
- **Email Service**: ⚠️ Configured (SMTP not connected)
- **Rate Limiting**: ✅ Active

---

**Last Updated:** 2025-05-24  
**Version:** 2.0.0  
**Status:** Active Development - Phase 20+ Complete - AUTH System Tested & Verified
