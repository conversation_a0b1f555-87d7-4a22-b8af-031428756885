// Test script để kiểm tra API proxy
const axios = require('axios');

const PROXY_URL = 'http://localhost:4000/api';

async function testProxyLogin() {
  try {
    console.log('🔍 Testing API proxy...');
    console.log('Proxy URL:', PROXY_URL);
    
    const response = await axios.post(`${PROXY_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123456'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Proxy login successful!');
    console.log('Status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Proxy login failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('Network error - no response received');
      console.log('Error:', error.message);
    } else {
      console.log('Error:', error.message);
    }
  }
}

testProxyLogin();
